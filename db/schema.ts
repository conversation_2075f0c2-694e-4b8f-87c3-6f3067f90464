import {
  boolean,
  integer,
  jsonb,
  pgTable,
  serial,
  text,
  timestamp,
} from 'drizzle-orm/pg-core';

export const logosTable = pgTable('logos_table', {
  id: serial('id').primaryKey(),
  image_url: text('link').notNull(),
  primary_color: text('primary_color').notNull(),
  background_color: text('background_color').notNull(),
  username: text('username').notNull(),
  userId: text('user_id').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  // Enhanced project fields
  projectName: text('project_name').notNull(), // Made required
  prompt: text('prompt'),
  style: text('style'),
  isDeleted: boolean('is_deleted').default(false),
  isFavorite: boolean('is_favorite').default(false),
  editCount: integer('edit_count').default(0),
  metadata: jsonb('metadata'),
});

export type InsertLogo = typeof logosTable.$inferInsert;
export type SelectLogo = typeof logosTable.$inferSelect;

export const userCreditsTable = pgTable('user_credits', {
  userId: text('user_id').primaryKey(),
  plan: text('plan').notNull(),
  creditsRemaining: integer('credits_remaining').notNull(),
  refilledAt: timestamp('refilled_at').notNull(),
});

export const creditTransactionsTable = pgTable('credit_transactions', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull(),
  delta: integer('delta').notNull(),
  reason: text('reason').notNull(),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});
