import { neon } from '@neondatabase/serverless';
import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/neon-http';

config({ path: '.env.local' });

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL is not defined');
}

const sql = neon(process.env.POSTGRES_URL);
export const db = drizzle(sql);

/**
 * Sets the current Clerk user id for Postgres RLS policies.
 * Must be called at the start of any request that hits the database.
 */
export async function setRlsUser(userId: string) {
  await sql(`select set_config('app.current_user_id', $1, true)`, [userId]);
}
