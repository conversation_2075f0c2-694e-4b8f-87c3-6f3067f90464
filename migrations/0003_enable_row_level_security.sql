-- Enable Row Level Security and add per-user policies
-- This migration ensures data isolation between users

-- Enable RLS on all tables
ALTER TABLE public.logos_table ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;
--> statement-breakpoint

-- Create user isolation policies for logos_table
DROP POLICY IF EXISTS logos_owner_isolation ON public.logos_table;
CREATE POLICY logos_owner_isolation ON public.logos_table
  FOR ALL
  TO public
  USING (user_id = current_setting('app.current_user_id', true))
  WITH CHECK (user_id = current_setting('app.current_user_id', true));
--> statement-breakpoint

-- Create user isolation policies for user_credits
DROP POLICY IF EXISTS user_credits_owner_isolation ON public.user_credits;
CREATE POLICY user_credits_owner_isolation ON public.user_credits
  FOR ALL
  TO public
  USING (user_id = current_setting('app.current_user_id', true))
  WITH CHECK (user_id = current_setting('app.current_user_id', true));
--> statement-breakpoint

-- Create user isolation policies for credit_transactions
DROP POLICY IF EXISTS credit_tx_owner_isolation ON public.credit_transactions;
CREATE POLICY credit_tx_owner_isolation ON public.credit_transactions
  FOR ALL
  TO public
  USING (user_id = current_setting('app.current_user_id', true))
  WITH CHECK (user_id = current_setting('app.current_user_id', true));

