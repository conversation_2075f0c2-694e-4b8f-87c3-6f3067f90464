CREATE TABLE IF NOT EXISTS "user_credits" (
  "user_id" text PRIMARY KEY NOT NULL,
  "plan" text NOT NULL,
  "credits_remaining" integer NOT NULL,
  "refilled_at" timestamp NOT NULL
);

CREATE TABLE IF NOT EXISTS "credit_transactions" (
  "id" serial PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "delta" integer NOT NULL,
  "reason" text NOT NULL,
  "metadata" jsonb,
  "created_at" timestamp DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS credit_tx_user_idx ON "credit_transactions" ("user_id");

