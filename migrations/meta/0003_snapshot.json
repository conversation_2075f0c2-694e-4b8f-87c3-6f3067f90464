{"id": "05ddea30-d232-47d4-bf7b-3dd2f06c7630", "prevId": "3354356c-6d32-4eea-ad50-10a5f79f94c2", "version": "7", "dialect": "postgresql", "tables": {"public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "delta": {"name": "delta", "type": "integer", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.logos_table": {"name": "logos_table", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": true}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": true}, "background_color": {"name": "background_color", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_credits": {"name": "user_credits", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true}, "credits_remaining": {"name": "credits_remaining", "type": "integer", "primaryKey": false, "notNull": true}, "refilled_at": {"name": "refilled_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}