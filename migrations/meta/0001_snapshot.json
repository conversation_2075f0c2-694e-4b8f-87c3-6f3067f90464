{"id": "3354356c-6d32-4eea-ad50-10a5f79f94c2", "prevId": "92bb7bd6-c570-49ca-8f5f-471ab5c0b5b8", "version": "7", "dialect": "postgresql", "tables": {"public.logos_table": {"name": "logos_table", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": true}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": true}, "background_color": {"name": "background_color", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}