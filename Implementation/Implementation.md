# Project Management Feature Implementation

## Feature Overview
Add project-based organization to the logo generation system. Users can create named projects, generate logos within projects, and manage their logo collections by project.

## Database Schema (Already Implemented)
- `projectName` field added to `logosTable` (required)
- Migration applied with RLS support
- API routes created: `/api/projects`, `/api/projects/create`, `/api/projects/[projectName]`

## User Workflow

### 1. Project Creation
- User clicks "Create New Project" button
- <PERSON><PERSON> opens with single input field for project name
- On submit: Creates project record in database, shows editor choice modal
- **Editor Choice Modal**: 
  - "AI Editor" - Navigate to chat-based logo generation
  - "Canvas Editor" - Navigate to manual design tool (Coming Soon - disabled)
- Project context passed to selected editor interface

### 2. Logo Generation Within Project
- AI editor shows current project name in header
- User generates logos through chat interface
- All generated logos automatically associated with current project
- Multiple generation sessions add more logos to same project

### 3. Projects List View
- Display all user projects as cards
- Show project name, logo count, thumbnail (latest logo), creation date
- Smart navigation:
  - Projects with 0 logos → redirect to AI editor (continue working)
  - Projects with logos → open project detail page
- Actions: rename, delete, favorite toggle

### 4. Project Detail Page
- Show all logo variants for specific project
- Display project metadata (name, creation date, logo count)
- Individual logo actions: download, view details
- Project actions: rename, delete, add more variants

## Technical Requirements

### UI Components Needed
1. Project creation modal (simple, clean design)
2. **Editor choice modal** (AI Editor vs Canvas Editor options)
3. Projects grid/list view page
4. Project detail page with logo gallery
5. Project context indicator in AI editor header

### Navigation Logic
- **After project creation** → Editor choice modal → Selected editor with project context
- **From projects list**:
  - Empty projects (logoCount = 0) → Editor choice modal (continue working)
  - Projects with logos (logoCount > 0) → Project detail page
- Maintain project context in selected editor until logos generated

### Session Management
- Pass project context from creation to selected editor
- Clear context after successful logo generation
- Handle browser navigation without losing project context

## Key Features
- **Simple Project Creation**: Single input, no complex forms
- **Editor Choice**: Users choose between AI Editor or Canvas Editor
- **Smart Routing**: Context-aware navigation based on project state
- **Project Persistence**: Projects saved immediately, never lost
- **Clean Organization**: Logos grouped by meaningful project names
- **Seamless Workflow**: Create → Choose Editor → Generate → Organize in one flow

## UI/UX Design System Rules

### Component Architecture
The platform uses **Radix UI Themes** as the foundation with custom wrapper components for legacy compatibility and consistency.

### Core Design Principles
- **Radix-First**: All UI components must use Radix UI primitives as base
- **Mapping Layer**: Legacy variant names map to Radix variants for backward compatibility
- **TypeScript Strict**: All components require proper TypeScript interfaces
- **Consistent Sizing**: Use Radix's standardized size system ('1', '2', '3')

### Required UI Components

#### 1. Button Component (`components/ui/button.tsx`)
```typescript
// REQUIRED: Use Button component with these variants only
<Button variant="default" />    // → Radix 'solid'
<Button variant="outline" />    // → Radix 'classic'  
<Button variant="secondary" />  // → Radix 'soft'
<Button variant="ghost" />      // → Radix 'soft'
<Button variant="destructive" /> // → Radix 'solid' (red)

// REQUIRED: Use these sizes only
<Button size="sm" />     // → Radix '1'
<Button size="default" /> // → Radix '2'  
<Button size="lg" />     // → Radix '3'
<Button size="icon" />   // → Radix '2' (square)
```

#### 2. Card Components (`components/ui/card.tsx`)
```typescript
// REQUIRED: Use Card structure with these components
<Card>
  <CardHeader>
    <CardTitle>Project Name</CardTitle>
    <CardDescription>Optional description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content */}
  </CardContent>
  <CardFooter>
    {/* Actions */}
  </CardFooter>
</Card>
```

#### 3. Input Components
```typescript
// Basic input - REQUIRED: Use standard styling
<Input placeholder="Enter project name" />

// Textarea - REQUIRED: Use Radix TextArea with size="2"
<Textarea placeholder="Additional information" />
```

#### 4. Badge Component (`components/ui/badge.tsx`)
```typescript
// REQUIRED: Use Badge with these variants only
<Badge variant="default" />     // → Radix 'solid'
<Badge variant="secondary" />   // → Radix 'soft'
<Badge variant="destructive" /> // → Radix 'solid' (red)
<Badge variant="outline" />     // → Radix 'surface'
```

#### 5. Toast System (`components/ui/toast.tsx`)
```typescript
// REQUIRED: Use toast variants for feedback
toast({
  title: "Success",
  description: "Project created successfully",
  variant: "default" // or "destructive" or "success"
})
```

### Strict Component Rules

1. **NO Custom Styling**: Never override Radix theme styles directly
2. **Use Mapping Functions**: Always use the variant mapping functions 
3. **TypeScript Required**: All props must be properly typed
4. **Consistent Patterns**: Follow existing component patterns exactly
5. **No Inline Styles**: Use className composition only
6. **Radix Size System**: Use '1', '2', '3' size system, not 'sm', 'md', 'lg'

### Layout Standards

#### Navigation Bar
- Fixed positioning with backdrop blur
- Height: h-16
- Max width: max-w-7xl with mx-auto
- Include CreditsPill for authenticated users

#### Modals & Dialogs
- Use Radix Dialog primitives 
- Consistent padding and spacing
- Proper focus management
- ESC key to close

#### Forms
- Use Radix Form components when available
- Consistent field spacing
- Proper validation states
- Loading states for submissions

### Project-Specific Requirements

#### Project Creation Modal
```typescript
<Card className="w-full max-w-md">
  <CardHeader>
    <CardTitle>Create New Project</CardTitle>
    <CardDescription>Enter a name for your logo project</CardDescription>
  </CardHeader>
  <CardContent>
    <Input placeholder="Project name..." />
  </CardContent>
  <CardFooter className="flex gap-2">
    <Button variant="outline">Cancel</Button>
    <Button variant="default">Create Project</Button>
  </CardFooter>
</Card>
```

#### Editor Choice Modal  
```typescript
<Card className="w-full max-w-md">
  <CardHeader>
    <CardTitle>Choose Your Editor</CardTitle>
  </CardHeader>
  <CardContent className="grid gap-4">
    <Button variant="default" size="lg">AI Editor</Button>
    <Button variant="outline" size="lg" disabled>
      Canvas Editor 
      <Badge variant="secondary" className="ml-2">Coming Soon</Badge>
    </Button>
  </CardContent>
</Card>
```

### Accessibility Requirements
- All interactive elements must be keyboard accessible
- Proper ARIA labels on all form controls
- Focus indicators on all interactive elements
- Screen reader compatible component structure

### Performance Rules
- Use React.memo for expensive rendering components
- Implement proper loading states
- Optimize image rendering with next/image
- Lazy load non-critical components

## Success Criteria
1. Users can create and name projects before generating logos
2. Users can choose between AI Editor and Canvas Editor after project creation
3. Canvas Editor shows "Coming Soon" state (disabled but visible)
4. All logos automatically organized under correct project
5. Projects persist even if user navigates away during creation
6. Clean transition from empty project to populated project
7. No duplicate projects or orphaned logos
8. Intuitive navigation based on project content state
9. **UI Consistency**: All components follow the Radix UI design system exactly
10. **Component Reusability**: No custom components outside the design system