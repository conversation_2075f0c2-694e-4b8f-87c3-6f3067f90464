# CLAUDE.md

## 🎯 Project Overview

Superlogo is an AI-powered logo generator built with Next.js 15, TypeScript, and PostgreSQL. Chat-first interface with credit-based SaaS model.

**Core Features:**

- Chat-first logo generation with Assistant UI
- Multi-model AI: FLUX Schnell/Dev, Stability AI SDXL
- 6 styles: Minimal, Tech, Corporate, Creative, Abstract, Flashy
- Credit-based plans: Free (3), Starter (50), Pro (200), Elite (600) credits/month
- Clerk authentication with entitlement-based plan management
- Gallery/history with watermarked downloads for free users

## 🔧 Code Quality Rules

Before you code anything, make sure you analyze the existing coding standards and rules to make everything seamless and keep the code quality to the next level.

- No experimental or temporary code
- Every line must be intentional and purposeful
- Follow established patterns exactly
- Maintain consistency across the entire platform
- **Simplicity First**: Choose the simplest solution that works - avoid over-engineering
- **3 Lines Better Than 10**: If you can solve it simply, do it

- **NEVER** disable linting rules or use placeholders
- **ALWAYS** run `pnpm run lint` and `pnpm run format` before completion
- No `any` types - create proper types instead
- JSDoc required for all functions
- Files must be kebab-case: `my-component.tsx`

## 🏗️ Architecture

### Tech Stack

- **Next.js 15.4.6** with App Router
- **React 19.1.1** + **TypeScript 5.9.2**
- **PostgreSQL** on NeonDB with Row-Level Security
- **Drizzle ORM 0.44.4** with Neon Serverless Driver
- **Clerk 6.30.1** for authentication
- **Upstash Redis** for rate limiting
- **Nebius AI** (OpenAI-compatible) for logo generation
- **Shadcn/UI** + **Radix UI Themes 3.2.1**
- **Assistant UI React 0.10.41** for chat interface

### Project Structure

```
app/
├── api/                     # API Routes (use these, not server actions)
│   ├── generate/           # Main logo generation
│   ├── credits/            # Credit information
│   ├── gallery/            # User gallery
│   ├── history/            # User history
│   ├── download/           # Download with watermarking
│   └── clerk/webhooks/     # Subscription webhooks
├── generate/               # Chat interface
├── gallery/                # User gallery
├── history/                # Personal history
└── pricing/                # Clerk PricingTable

components/
├── assistant-ui/           # Chat components
├── ui/                     # Shadcn/UI components
└── landing/                # Landing page sections

lib/
├── ai.ts                   # Nebius AI client
├── credits.ts              # Credit management
├── plan.ts                 # Plan logic
└── upstash.ts              # Rate limiting

db/
├── schema.ts               # Three-table schema
└── index.ts                # Neon connection with RLS
```

## 🛠️ Development Rules

### 1) Server vs Client Boundaries

- Use API Routes (`app/api/*/route.ts`) for all database/AI operations
- Always `await` Next.js helpers: `const { userId } = await auth();`
- Client components use `fetch()` to call API routes

### 2) Database Operations

- **ALWAYS** call `setRlsUser(userId)` before database operations
- Three tables: `logosTable`, `userCreditsTable`, `creditTransactionsTable`
- All operations require authentication

### 3) Rate Limiting & Credits

- Burst protection: `createFixedWindowLimiter(30, '1 m', 'superlogo:burst')`
- Credit functions: `decrementCredit()`, `getRemainingCredits()`, `ensureUserCredits()`
- Plans: free(3), starter(50), pro(200), elite(600) credits/month

### 4) AI Integration

- Use `getOpenAIClient()` from `lib/ai`
- Models: `black-forest-labs/flux-schnell` (default), `flux-dev`, `stability-ai/sdxl`
- Validate inputs with Zod schemas

### 5) Authentication & Plans

- Derive plan: `getUserPlanFromEntitlements()` from `lib/plan`
- Protected routes: `/generate`, `/history`, `/gallery`, `/account/billing`

## 🗄️ Database Migrations

### Migration Workflow

**Schema Changes:**

1. Update `db/schema.ts`
2. Run `pnpm run db:generate`
3. Review generated SQL
4. Run `pnpm run db:migrate`

**Security/RLS Changes:**

1. Create `migrations/XXXX_descriptive_name.sql`
2. Update `migrations/meta/_journal.json`
3. Run `pnpm run db:migrate`

### Commands

```bash
pnpm run db:generate     # Generate migration
pnpm run db:migrate      # Apply migrations
pnpm run db:push         # DEV ONLY - resets RLS!
```

### Database Schema

```typescript
logosTable {
  id: serial (PK)
  image_url: text (maps to 'link' column)
  primary_color: text
  background_color: text
  username: text
  userId: text
  createdAt: timestamp
}

userCreditsTable {
  userId: text (PK)
  plan: 'free' | 'starter' | 'pro' | 'elite'
  creditsRemaining: integer
  refilledAt: timestamp
}

creditTransactionsTable {
  id: serial (PK)
  userId: text
  delta: integer (-1 for generation, +X for refill)
  reason: 'generation' | 'refill'
  metadata: jsonb
  createdAt: timestamp
}
```

### Critical Rules

- **NEVER** use `db:push` in production (resets RLS)
- **ALWAYS** use `db:migrate` for production
- **NEVER** modify applied migration files

## 🔑 Environment Variables

```env
POSTGRES_URL=postgresql://...
NEBIUS_API_KEY=...
HELICONE_API_KEY=...                    # Optional
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=...
CLERK_SECRET_KEY=...
CLERK_WEBHOOK_SECRET=...
UPSTASH_REDIS_REST_URL=...
UPSTASH_REDIS_REST_TOKEN=...
```

## 🎨 AI Prompt Engineering

Style prompts in `styleLookup`:

```typescript
const styleLookup = {
  minimal:
    "minimal, simple, timeless, single color logo, use negative space...",
  tech: "highly detailed, sharp focus, clean lines, neutral colors...",
  corporate: "modern, geometric shapes, natural colors, negative space...",
  creative: "playful, bright bold colors, rounded shapes, lively...",
  abstract: "artistic, unique shapes, patterns, textures...",
  flashy: "bold, futuristic, vibrant neon colors, metallic accents...",
};
```

## 🚀 Commands

```bash
# Development
pnpm run dev
pnpm run build
pnpm run lint
pnpm run format

# Database
pnpm run db:generate
pnpm run db:migrate
pnpm run db:studio
```

## 🔒 Security Features

- Row-Level Security policies on all tables
- Zod validation for all API inputs
- HMAC webhook verification
- Watermarking for free users
- Credit-based usage limits

Don't commit changes until I ask you to.
