declare module '@neondatabase/serverless' {
  export interface NeonQueryFn {
    (strings: TemplateStringsArray, ...params: unknown[]): Promise<unknown>;
    (query: string, params?: unknown[], opts?: unknown): Promise<unknown>;
    transaction?: (
      queries: unknown[] | ((fn: unknown) => unknown[])
    ) => Promise<unknown[]>;
  }
  export function neon(
    connectionString: string,
    options?: unknown
  ): NeonQueryFn;
}
