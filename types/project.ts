/**
 * Shared Project types for the app.
 * Keep UI/client-safe only; do not include server-only fields here.
 */

/**
 * Represents a logo generation project.
 */
export interface Project {
  id: string;
  projectName: string;
  thumbnail: string | null;
  logoCount: number;
  createdAt: string;
  lastModified: string;
  isFavorite: boolean;
  prompt?: string;
  style?: string;
}

/**
 * Available view modes for projects page.
 */
export type ViewMode = 'grid' | 'list';
