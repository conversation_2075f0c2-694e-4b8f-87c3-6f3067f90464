import { useCallback, useState } from 'react';
import type { Project } from '@/types/project';

/**
 * Manages projects list data + common mutations.
 * Keeps network logic colocated and exposes simple functions for the UI.
 *
 * All functions return booleans to indicate success; UI decides to toast.
 */
export function useProjects() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deletingProjectId, setDeletingProjectId] = useState<string | null>(
    null
  );

  /**
   * Load all projects (initial call on mount).
   */
  const loadAll = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/projects');
      const fetchedProjects = res.ok ? await res.json() : null;
      if (!fetchedProjects) {
        return false;
      }
      setProjects(fetchedProjects as Project[]);
      return true;
    } catch {
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh list without forcing loading spinner.
   */
  const refresh = useCallback(async (): Promise<boolean> => {
    try {
      const res = await fetch('/api/projects');
      const fetchedProjects = res.ok ? await res.json() : null;
      if (!fetchedProjects) {
        return false;
      }
      setProjects(fetchedProjects as Project[]);
      return true;
    } catch {
      return false;
    }
  }, []);

  /**
   * Toggle favorite with optimistic UI and revert on failure.
   */
  const toggleFavorite = useCallback(
    async (project: Project): Promise<boolean> => {
      const next = !project.isFavorite;
      setProjects((prev) =>
        prev.map((p) => (p.id === project.id ? { ...p, isFavorite: next } : p))
      );
      try {
        const res = await fetch(
          `/api/projects/${encodeURIComponent(project.projectName)}`,
          {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ isFavorite: next }),
          }
        );
        if (!res.ok) {
          throw new Error('Failed to update favorite');
        }
        return true;
      } catch {
        // Revert
        setProjects((prev) =>
          prev.map((p) =>
            p.id === project.id ? { ...p, isFavorite: !next } : p
          )
        );
        return false;
      }
    },
    []
  );

  /**
   * Delete project by name/id and update list.
   */
  const deleteProject = useCallback(
    async (projectName: string, projectId: string): Promise<boolean> => {
      setDeletingProjectId(projectId);
      try {
        const response = await fetch(
          `/api/projects/${encodeURIComponent(projectName)}`,
          { method: 'DELETE' }
        );
        if (!response.ok) {
          throw new Error('Failed to delete project');
        }
        setProjects((prev) => prev.filter((p) => p.id !== projectId));
        return true;
      } catch {
        return false;
      } finally {
        setDeletingProjectId(null);
      }
    },
    []
  );

  /**
   * Update project name in local state (used on successful rename modal).
   */
  const updateProjectName = useCallback((oldName: string, newName: string) => {
    setProjects((prev) =>
      prev.map((p) =>
        p.projectName === oldName ? { ...p, projectName: newName } : p
      )
    );
  }, []);

  return {
    projects,
    isLoading,
    deletingProjectId,
    loadAll,
    refresh,
    toggleFavorite,
    deleteProject,
    updateProjectName,
  };
}
