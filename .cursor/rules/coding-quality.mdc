---
alwaysApply: true
---

### Zero Tolerance for Errors

- **NEVER** disable linting rules
- **NEVER** take shortcuts or use placeholders like `// TODO:` or `/* Implementation needed */`
- **ALWAYS** fix issues properly, not work around them with hacks or temporary solutions
- **ALWAYS** run `pnpm run lint` before considering any implementation complete
- **ALWAYS** run `pnpm run format` to ensure consistent code formatting

### Production-Grade Code Only

- **No experimental or temporary code** - Every line must be production-ready
- **Every line must be intentional and purposeful** - No dead code or unused imports
- **Simplicity First**: Choose the simplest solution that works - avoid over-engineering
- **3 Lines Better Than 10**: If you can solve it simply, do it - prioritize readability

### Documentation Standards

- **JSDoc MANDATORY** for all functions: Include purpose, params, returns, throws, and example
- **Comments for WHY, not WHAT**: Business logic reasoning, external API quirks, design decisions
- **Never comment obvious code**: No "Get user by ID" or repeating function names

### Accuracy Requirements

- **NEVER guess or assume** - verify every API, prop, or configuration against official docs
- **Use only official documentation sources**

### Code Quality Standards

Follow these rules to avoid lint/build errors and keep code production‑grade. All items below are enforced by our tooling (Ultracite/Biome) and recent fixes in this repo.

## 1) File and naming conventions

- Files must be kebab-case: `my-component.tsx`, not `MyComponent.tsx`.
- One component/file unless a clear, small, related group (e.g., shadcn UI parts).

## 2) Control flow and style

- Always use block statements for conditionals and loops.
  - Good: `if (cond) { doThing(); }`
  - Bad: `if (cond) doThing();`
- No unused variables, imports, or React fragments.
- Prefer early returns; keep nesting shallow.

## 3) TypeScript strictness

- No `any`. Create or refine types instead.
- Don’t mark functions `async` unless they await something.
- Use explicit types for external payloads (e.g., Clerk claims, API bodies).

## 4) Server vs client boundaries

- Do not import server actions directly into client components.
  - Use Route Handlers (`app/api/.../route.ts`) and `fetch` from clients OR call Server Actions from server components/forms.
- In Route Handlers, always `await` Next’s server helpers:
  - `const { userId, sessionClaims } = await auth();`
  - `const h = await headers();`
  - `const c = await cookies();`
- Server redirects belong in server components/handlers only; clients should use `router.replace` in an effect.

## 5) Rate limiting & credits

- Use `createFixedWindowLimiter(limit, window, prefix)` from `lib/upstash` for burst protection (30 req/min).
- Credit management via `decrementCredit()`, `getRemainingCredits()`, `ensureUserCredits()` from `lib/credits`.
- All users require authentication - no anonymous generation.

## 6) Plans & entitlements

- Derive plan via `getUserPlanFromEntitlements()` from `lib/plan`.
- Keep plan logic server-side; clients only reflect UI state.

## 7) AI and external calls

- Use `getOpenAIClient()` from `lib/ai`.
- Validate all inputs with `zod` schemas in Route Handlers.

## 8) DB access (Neon + Drizzle)

- Use `db` from `db/index.ts` (Neon driver). Don’t instantiate drivers per request.
- Persist only for signed-in users unless explicitly required.

## 9) Toasts and UX

- Use `useToast()` from `hooks/use-toast` for user feedback.
- Surface clear upgrade/limit messages on 429 responses.

## 10) Commands to run locally

- Lint: `pnpm run lint`
- Format: `pnpm run format`
