# Master Rules (Read Before Coding)

- Build: `pnpm run build`

## 11) Environment variables (required)

- `POSTGRES_URL`, `NEBIUS_API_KEY`, `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`, `CLERK_SECRET_KEY`, `UPSTASH_REDIS_REST_URL`, `UPSTASH_REDIS_REST_TOKEN`, `NEXT_PUBLIC_DEVELOPMENT_URL`. Optional: `NEXT_PUBLIC_APP_URL`, `HELICONE_API_KEY`.

## 12) PR checklist

- No lint errors; format applied.
- No `any`, no unused `async`, no single-line `if`.
- No client imports of server-only modules.
- API handlers validate inputs and handle errors.

Keeping to these rules prevents the exact classes of issues we fixed: naming violations, missing blocks, unused async, unsafe `any`, server/client misuse, and missing awaits for Next server helpers.

