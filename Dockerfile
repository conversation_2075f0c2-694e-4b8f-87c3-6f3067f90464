# Build Stage
FROM node:20-alpine AS builder
WORKDIR /usr/src/app

# Receive the required build variable
ARG NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}

# Copy dependency files and install them
COPY package.json pnpm-lock.yaml ./
RUN corepack enable && corepack prepare pnpm@9.0.0 --activate
RUN pnpm install --frozen-lockfile
# Copy the rest of the source code and create a production build
COPY . .
RUN pnpm run build

# Production Stage
FROM node:20-alpine AS runner
WORKDIR /usr/src/app
ENV NODE_ENV=production
# Copy artifacts generated in the build stage
COPY --from=builder /usr/src/app/.next ./.next
COPY --from=builder /usr/src/app/public ./public
COPY --from=builder /usr/src/app/package.json ./
COPY --from=builder /usr/src/app/pnpm-lock.yaml ./
# Install only production dependencies
RUN corepack enable && corepack prepare pnpm@9.0.0 --activate
RUN pnpm install --prod --frozen-lockfile
EXPOSE 3000
CMD ["pnpm", "start"]

