'use client';

import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { ArrowLeft, Plus } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import LogoCard from '@/components/logo-card';
import SkeletonCard from '@/components/skeleton-card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/ui/navigation';
import type { SelectLogo } from '@/db/schema';
import { useToast } from '@/hooks/use-toast';
import { domain } from '@/lib/domain';

/**
 * Project detail page
 * Shows all logos for a specific project with project metadata
 */
export default function ProjectDetailPage() {
  const { toast } = useToast();
  const router = useRouter();
  const params = useParams();
  const projectName = decodeURIComponent(params.projectName as string);

  const [logos, setLogos] = useState<SelectLogo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [_isDownloading, setIsDownloading] = useState(false);

  useEffect(() => {
    const fetchProjectLogos = async () => {
      try {
        const res = await fetch(
          `/api/projects/${encodeURIComponent(projectName)}`
        );
        const fetchedLogos = res.ok ? await res.json() : null;
        if (fetchedLogos) {
          setLogos(fetchedLogos);
        } else {
          toast({
            title: 'Error',
            description: 'Failed to load project logos',
            variant: 'destructive',
          });
        }
      } catch {
        toast({
          title: 'Error',
          description: 'Failed to load project logos',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    fetchProjectLogos();
  }, [projectName, toast]);

  const handleDownload = async (imageUrl: string) => {
    setIsDownloading(true);
    try {
      const res = await fetch('/api/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: imageUrl }),
      });
      const result = res.ok ? await res.json() : null;
      if (result?.data) {
        const a = document.createElement('a');
        a.href = result.data;
        a.download = 'logo.webp';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        toast({
          title: 'Download started',
          description: 'Your logo is being downloaded',
        });
      } else {
        throw new Error('Failed to download logo');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred while downloading',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleAddMoreVariants = () => {
    const latest = actualLogos.at(-1);
    const qp = new URLSearchParams({ project: projectName });
    if (latest?.prompt) {
      qp.set('prompt', latest.prompt);
    }
    if (latest?.style) {
      qp.set('style', latest.style);
    }
    if (latest?.primary_color) {
      qp.set('primaryColor', latest.primary_color);
    }
    if (latest?.background_color) {
      qp.set('backgroundColor', latest.background_color);
    }
    router.push(`/generate?${qp.toString()}`);
  };

  const handleOpenInGeneratorProject = () => {
    // Open the generator at project-level so the right-side history sidebar can load
    const qp = new URLSearchParams({ project: projectName });
    router.push(`/generate?${qp.toString()}`);
  };

  /**
   * Type guard to safely read metadata.cover without using any.
   */
  const isCoverLogo = (logo: SelectLogo): boolean => {
    const md = logo.metadata as unknown;
    if (!md || typeof md !== 'object') {
      return false;
    }
    const cover = (md as Record<string, unknown>).cover;
    return cover === true;
  };

  /**
   * Set a logo as the project's cover image.
   * Calls POST /api/projects/[projectName]/cover with { logoId }.
   */
  const handleSetCover = async (logoId: number) => {
    try {
      const res = await fetch(
        `/api/projects/${encodeURIComponent(projectName)}/cover`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ logoId }),
        }
      );

      if (!res.ok) {
        throw new Error('Failed to set cover image');
      }

      // Refresh project logos to reflect new cover state
      const refreshed = await fetch(
        `/api/projects/${encodeURIComponent(projectName)}`
      );
      if (refreshed.ok) {
        const data: SelectLogo[] = await refreshed.json();
        setLogos(data);
      }

      toast({ title: 'Cover updated', description: 'Project cover set.' });
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to set cover image',
        variant: 'destructive',
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Filter out placeholder logos (empty image_url)
  const actualLogos = logos.filter(
    (logo) => logo.image_url && logo.image_url.trim() !== ''
  );

  return (
    <div className="min-h-screen">
      <Navigation />
      <div className="mx-auto mt-20 max-w-5xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6 flex items-start justify-between">
          <div className="flex items-start gap-3">
            <Button asChild size="icon" variant="ghost">
              <Link href="/projects">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="font-semibold text-3xl leading-tight">
                {projectName}
              </h1>
              <p className="mt-1 text-muted-foreground text-sm">
                {actualLogos.length === 0
                  ? 'No logos yet'
                  : `${actualLogos.length} logo${actualLogos.length === 1 ? '' : 's'}`}
                {actualLogos.length > 0 && actualLogos.at(-1)?.createdAt && (
                  <span className="ml-2">
                    • Created{' '}
                    {formatDate(
                      actualLogos.at(-1)?.createdAt?.toString() || ''
                    )}
                  </span>
                )}
              </p>
            </div>
          </div>

          <SignedIn>
            <div className="flex items-center gap-2">
              <Button
                onClick={handleOpenInGeneratorProject}
                variant="secondary"
              >
                Open in Generator
              </Button>
              <Button className="gap-2" onClick={handleAddMoreVariants}>
                <Plus className="h-4 w-4" />
                Add More Variants
              </Button>
            </div>
          </SignedIn>
        </div>

        {/* Summary */}
        {actualLogos.length > 0 && (
          <div className="mb-8 rounded-xl border bg-card p-4">
            <div className="grid gap-6 md:grid-cols-3">
              <div className="md:col-span-2">
                <div className="flex items-center gap-2">
                  {actualLogos.at(-1)?.style && (
                    <span className="rounded-md border px-1.5 py-0.5 text-[11px]">
                      {actualLogos.at(-1)?.style}
                    </span>
                  )}
                  <span className="text-muted-foreground text-xs">Prompt</span>
                </div>
                {actualLogos.at(-1)?.prompt && (
                  <p
                    className="mt-1 line-clamp-3 text-sm"
                    title={actualLogos.at(-1)?.prompt ?? undefined}
                  >
                    {actualLogos.at(-1)?.prompt}
                  </p>
                )}
              </div>
              <div>
                <div className="text-muted-foreground text-xs">Colors</div>
                <div className="mt-2 flex items-center gap-3">
                  <div
                    className="h-5 w-5 rounded-[6px] border"
                    style={{
                      backgroundColor: actualLogos.at(-1)?.primary_color,
                    }}
                    title="Primary Color"
                  />
                  <div
                    className="h-5 w-5 rounded-[6px] border"
                    style={{
                      backgroundColor: actualLogos.at(-1)?.background_color,
                    }}
                    title="Background Color"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        <SignedOut>
          <div className="rounded-md border p-6 text-center">
            <div className="mb-2 font-medium">Sign in to view your project</div>
            <SignInButton
              forceRedirectUrl={`${domain}/projects/${encodeURIComponent(projectName)}`}
              signUpForceRedirectUrl={`${domain}/projects/${encodeURIComponent(projectName)}`}
            >
              <Button>Sign In</Button>
            </SignInButton>
          </div>
        </SignedOut>

        <SignedIn>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3">
            {isLoading &&
              [...new Array(6)].map(() => (
                <SkeletonCard key={crypto.randomUUID()} />
              ))}

            {!isLoading &&
              actualLogos.length > 0 &&
              actualLogos.map((logo) => (
                <LogoCard
                  isCover={isCoverLogo(logo)}
                  key={logo.id}
                  logo={logo}
                  onDownload={() => handleDownload(logo.image_url)}
                  onSetCover={handleSetCover}
                  showColors={false}
                  showMeta={false}
                  showOpenInGenerator={false}
                />
              ))}

            {!isLoading && actualLogos.length === 0 && (
              <div className="col-span-full py-12 text-center text-muted-foreground">
                <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                  <Plus className="h-5 w-5" />
                </div>
                <div className="mb-1 font-medium">
                  No logos in this project yet
                </div>
                <p className="mb-3 text-sm">
                  Start generating logos to populate this project.
                </p>
                <Button onClick={handleAddMoreVariants}>
                  Generate your first logo
                </Button>
              </div>
            )}
          </div>
        </SignedIn>
      </div>
    </div>
  );
}
