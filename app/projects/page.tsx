'use client';

import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { LayoutGrid, List as ListIcon, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { EditorChoiceModal } from '@/components/editor-choice-modal';
import { ProjectCreationModal } from '@/components/project-creation-modal';
import { ProjectRenameModal } from '@/components/project-rename-modal';
import { GridProjectCard } from '@/components/projects/grid-project-card';
import { ListProjectCard } from '@/components/projects/list-project-card';
import SkeletonCard from '@/components/skeleton-card';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import Navigation from '@/components/ui/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useProjects } from '@/hooks/use-projects';
import { useToast } from '@/hooks/use-toast';
import { domain } from '@/lib/domain';
import {
  filterProjects,
  getSavedViewMode,
  saveViewMode,
  sortProjects,
} from '@/lib/projects';
import type { Project, ViewMode } from '@/types/project';

interface ProjectCardProps {
  project: Project;
  isDeleting: boolean;
  isDropdownOpen: boolean;
  mode: ViewMode;
  onClick: (project: Project) => void;
  onToggleFavorite: (project: Project) => void;
  onOpenRenameModal: (projectId: string, name: string) => void;
  onDeleteProject: (projectName: string, projectId: string) => void;
  setDropdownOpen: (open: boolean) => void;
}

function ProjectCard(props: ProjectCardProps) {
  const {
    project,
    isDeleting,
    isDropdownOpen,
    mode,
    onClick,
    onToggleFavorite,
    onOpenRenameModal,
    onDeleteProject,
    setDropdownOpen,
  } = props;

  const childProps = {
    project,
    isDeleting,
    isDropdownOpen,
    onToggleFavorite,
    onOpenRenameModal,
    onDeleteProject,
    setDropdownOpen,
  } as const;

  return (
    <Card
      className="group hover:-translate-y-1 cursor-pointer overflow-hidden rounded-2xl border bg-card/50 shadow-md backdrop-blur-sm transition-all duration-300 hover:shadow-primary/10 hover:shadow-xl"
      onClick={() => onClick(project)}
    >
      {mode === 'grid' ? (
        <GridProjectCard {...childProps} />
      ) : (
        <ListProjectCard {...childProps} />
      )}
    </Card>
  );
}

/**
 * Projects list page
 * Shows all user projects as cards with smart navigation logic
 */
/**
 * Projects list page
 *
 * Renders the authenticated user's projects with search, sort, favorite toggle,
 * rename and delete actions. Empty projects open the editor choice flow.
 *
 * @returns JSX page for projects
 */
export default function ProjectsPage() {
  const { toast } = useToast();
  const router = useRouter();
  const {
    projects,
    isLoading,
    deletingProjectId,
    loadAll,
    toggleFavorite,
    deleteProject,
    updateProjectName,
  } = useProjects();
  const [showEditorChoice, setShowEditorChoice] = useState(false);
  const [selectedProjectName, setSelectedProjectName] = useState('');
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [renameProjectData, setRenameProjectData] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [query, setQuery] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'a-z' | 'favorites'>(
    'recent'
  );
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  useEffect(() => {
    (async () => {
      const ok = await loadAll();
      if (!ok) {
        toast({
          title: 'Error',
          description: 'Failed to load projects',
          variant: 'destructive',
        });
      }
    })();
  }, [loadAll, toast]);

  // Initialize view mode from localStorage (client-only)
  useEffect(() => {
    const saved = getSavedViewMode();
    setViewMode(saved);
  }, []);

  const handleProjectCreated = (projectName: string) => {
    setSelectedProjectName(projectName);
    setShowEditorChoice(true);
    // Refresh projects list (show loading)
    loadAll();
  };

  const handleProjectClick = (project: Project) => {
    if (project.logoCount === 0) {
      // Empty project - show editor choice modal
      setSelectedProjectName(project.projectName);
      setShowEditorChoice(true);
    } else {
      // Project with logos - navigate to project detail page
      router.push(`/projects/${encodeURIComponent(project.projectName)}`);
    }
  };

  const handleToggleFavorite = async (project: Project) => {
    const ok = await toggleFavorite(project);
    if (!ok) {
      toast({
        title: 'Error',
        description: 'Failed to update favorite',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteProject = async (
    projectName: string,
    projectId: string
  ) => {
    if (
      !confirm(
        `Are you sure you want to delete "${projectName}"? This will permanently delete the project and all its logos.`
      )
    ) {
      return;
    }
    const ok = await deleteProject(projectName, projectId);
    if (ok) {
      toast({
        title: 'Project deleted',
        description: `"${projectName}" and all its logos have been deleted.`,
      });
    } else {
      toast({
        title: 'Error',
        description: 'Failed to delete project',
        variant: 'destructive',
      });
    }
  };

  const handleProjectRenamed = (oldName: string, newName: string) => {
    updateProjectName(oldName, newName);
  };

  const handleOpenRenameModal = (projectId: string, projectName: string) => {
    setRenameProjectData({ id: projectId, name: projectName });
    setRenameModalOpen(true);
    setOpenDropdownId(null); // Close dropdown when modal opens
  };

  // Derived, filtered and sorted list
  const base = filterProjects(projects, query);
  const filtered =
    sortBy === 'favorites' ? base.filter((p) => p.isFavorite) : base;
  const displayedProjects = sortProjects(filtered, sortBy);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="mx-auto mt-20 max-w-7xl px-6 py-12">
        <div className="mb-12 flex flex-col gap-4 sm:flex-row sm:items-end sm:justify-between">
          <div className="space-y-2">
            <h1 className="font-bold text-4xl tracking-tight">
              Your
              <span className="mx-3 text-primary">Projects</span>
            </h1>
            <p className="text-lg text-muted-foreground">
              Manage your logo projects and continue your creative journey
            </p>
          </div>
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
            <div className="flex gap-3">
              <Input
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search projects..."
                value={query}
              />
              <Select
                onValueChange={(v) => setSortBy(v as typeof sortBy)}
                value={sortBy}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Recent</SelectItem>
                  <SelectItem value="a-z">A–Z</SelectItem>
                  <SelectItem value="favorites">Favorites</SelectItem>
                </SelectContent>
              </Select>
              {/* View toggle */}
              <div className="hidden items-center rounded-md border bg-background p-0.5 sm:flex">
                <Button
                  aria-pressed={viewMode === 'grid'}
                  className={viewMode === 'grid' ? 'bg-muted' : ''}
                  onClick={() => {
                    setViewMode('grid');
                    saveViewMode('grid');
                  }}
                  size="icon"
                  variant="ghost"
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
                <Button
                  aria-pressed={viewMode === 'list'}
                  className={viewMode === 'list' ? 'bg-muted' : ''}
                  onClick={() => {
                    setViewMode('list');
                    saveViewMode('list');
                  }}
                  size="icon"
                  variant="ghost"
                >
                  <ListIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <SignedIn>
              <ProjectCreationModal onProjectCreated={handleProjectCreated}>
                <Button size="default">
                  <Plus className="h-4 w-4" />
                  Create New Project
                </Button>
              </ProjectCreationModal>
            </SignedIn>
          </div>
        </div>

        <SignedOut>
          <div className="mx-auto max-w-md rounded-2xl border bg-card/50 p-8 text-center shadow-lg backdrop-blur-sm">
            <div className="mb-4 font-semibold text-lg">
              Sign in to view your projects
            </div>
            <SignInButton
              forceRedirectUrl={`${domain}/projects`}
              signUpForceRedirectUrl={`${domain}/projects`}
            >
              <Button size="default">Sign In</Button>
            </SignInButton>
          </div>
        </SignedOut>

        <SignedIn>
          <div
            className={
              viewMode === 'grid'
                ? 'grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4'
                : 'grid grid-cols-1 gap-3'
            }
          >
            {isLoading &&
              [...new Array(6)].map(() => (
                <SkeletonCard key={crypto.randomUUID()} />
              ))}

            {!isLoading &&
              displayedProjects.length > 0 &&
              displayedProjects.map((project) => (
                <ProjectCard
                  isDeleting={deletingProjectId === project.id}
                  isDropdownOpen={openDropdownId === project.id}
                  key={project.id}
                  mode={viewMode}
                  onClick={handleProjectClick}
                  onDeleteProject={handleDeleteProject}
                  onOpenRenameModal={handleOpenRenameModal}
                  onToggleFavorite={handleToggleFavorite}
                  project={project}
                  setDropdownOpen={(open) =>
                    setOpenDropdownId(open ? project.id : null)
                  }
                />
              ))}

            {!isLoading && projects.length === 0 && (
              <div className="col-span-full py-16 text-center">
                <div className="mx-auto max-w-md space-y-6">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Plus className="h-8 w-8 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-xl">No projects yet</h3>
                    <p className="text-muted-foreground">
                      Create your first project to start generating amazing
                      logos with AI
                    </p>
                  </div>
                  <ProjectCreationModal onProjectCreated={handleProjectCreated}>
                    <Button size="default">Create your first project</Button>
                  </ProjectCreationModal>
                </div>
              </div>
            )}
          </div>
        </SignedIn>

        <EditorChoiceModal
          onOpenChange={setShowEditorChoice}
          open={showEditorChoice}
          projectName={selectedProjectName}
        />

        {/* Rename Modal - Independent of dropdown */}
        {renameProjectData && (
          <ProjectRenameModal
            onOpenChange={setRenameModalOpen}
            onProjectRenamed={handleProjectRenamed}
            open={renameModalOpen}
            projectId={renameProjectData.id}
            projectName={renameProjectData.name}
          />
        )}
      </div>
    </div>
  );
}
