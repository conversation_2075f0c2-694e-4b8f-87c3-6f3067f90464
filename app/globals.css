@import "tailwindcss";

@theme {
  --font-primary: var(--font-manrope), sans-serif;

  /* Colors - using existing CSS variables */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  /* TailwindCSS v4 Border Radius Design Tokens */
  /* These automatically create rounded-button, rounded-card, rounded-container utilities */
  --radius-button: calc(var(--radius) - 4px);
  --radius-card: calc(var(--radius) - 2px);
  --radius-container: var(--radius);

  /* (Removed) Legacy Tailwind radius support variables were unused */

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-shiny-text: shiny-text 8s infinite;
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes shiny-text {
  0%,
  90%,
  100% {
    background-position: calc(-100% - var(--shiny-width)) 0;
  }
  30%,
  60% {
    background-position: calc(100% + var(--shiny-width)) 0;
  }
}

/* Animation utilities to replace tailwindcss-animate usage */
/* Keep names compatible with existing component classnames */

/* Base helpers */
.animate-in {
  animation-duration: 200ms;
  animation-timing-function: cubic-bezier(0.2, 0, 0, 1);
  animation-fill-mode: both;
}
.animate-out {
  animation-duration: 150ms;
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
  animation-fill-mode: both;
}

/* Fade */
@keyframes fade-in-0 {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fade-in-0 {
  animation-name: fade-in-0;
}

@keyframes fade-out-0 {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fade-out-0 {
  animation-name: fade-out-0;
}

@keyframes fade-out-80 {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 0;
  }
}
.fade-out-80 {
  animation-name: fade-out-80;
}

/* Zoom */
@keyframes zoom-in-95 {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.zoom-in-95 {
  animation-name: zoom-in-95;
}

@keyframes zoom-out-95 {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}
.zoom-out-95 {
  animation-name: zoom-out-95;
}

/* Slide small (2 = 0.5rem) */
@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

@keyframes slide-in-from-bottom-2 {
  from {
    opacity: 0;
    transform: translateY(0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.slide-in-from-bottom-2 {
  animation-name: slide-in-from-bottom-2;
}

@keyframes slide-in-from-left-2 {
  from {
    opacity: 0;
    transform: translateX(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.slide-in-from-left-2 {
  animation-name: slide-in-from-left-2;
}

@keyframes slide-in-from-right-2 {
  from {
    opacity: 0;
    transform: translateX(0.5rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.slide-in-from-right-2 {
  animation-name: slide-in-from-right-2;
}

/* Slide half (1/2 = 50%) */
@keyframes slide-in-from-left-1-2 {
  from {
    transform: translateX(-50%);
  }
  to {
    transform: translateX(0);
  }
}
.slide-in-from-left-1\/2 {
  animation-name: slide-in-from-left-1-2;
}

@keyframes slide-out-to-left-1-2 {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}
.slide-out-to-left-1\/2 {
  animation-name: slide-out-to-left-1-2;
}

/* Slide full (100%) */
@keyframes slide-in-from-bottom-full {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
.slide-in-from-bottom-full {
  animation-name: slide-in-from-bottom-full;
}

@keyframes slide-out-to-right-full {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}
.slide-out-to-right-full {
  animation-name: slide-out-to-right-full;
}

/* Custom utilities for background patterns */
@utility bg-grid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='%23e5e7eb'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-grid-black {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='%23000000'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-grid-white {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='%23ffffff'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-grid-small {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='8' height='8' fill='none' stroke='%23e5e7eb'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-dot {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='%23e5e7eb' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}

@utility bg-dot-black {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='%23000000' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}

@utility bg-dot-white {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='%23ffffff' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}

@utility bg-checkered {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' width='20' height='20'%3e%3crect width='10' height='10' fill='%23f0f0f0'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f0f0f0'/%3e%3c/svg%3e");
}

/* TailwindCSS v4 automatically generates these utilities from @theme variables:
 * rounded-button, rounded-card, rounded-container
 * No need for custom @utility declarations anymore!
 */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 1rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 1rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings:
      "ss01" on,
      "ss02" on,
      "cv01" on,
      "cv02" on;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

html {
  scroll-behavior: smooth;
}

html,
body {
  overflow-x: hidden;
}
