import { openai } from '@ai-sdk/openai';
// import { frontendTools } from '@assistant-ui/react-ai-sdk';
import { streamText } from 'ai';

export const runtime = 'nodejs';
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, system } = await req.json();

  const result = streamText({
    model: openai('gpt-4o'),
    messages,
    system,
    // tools: frontendTools(tools),
  });

  return result.toTextStreamResponse();
}
