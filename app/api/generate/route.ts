import { auth } from '@clerk/nextjs/server';
import dedent from 'dedent';
//
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';
import { getOpenAIClient } from '@/lib/ai';
import { decrementCredit, ensureUserCredits } from '@/lib/credits';
import { getUserPlanFromEntitlements } from '@/lib/plan';
import { createFixedWindowLimiter } from '@/lib/upstash';

const FormSchema = z.object({
  companyName: z.string().min(1),
  style: z.string(),
  symbolPreference: z.string(),
  additionalInfo: z.string().optional(),
  primaryColor: z.string(),
  secondaryColor: z.string(),
  model: z.enum([
    'stability-ai/sdxl',
    'dall-e-3',
    'black-forest-labs/flux-schnell',
    'black-forest-labs/flux-dev',
  ]),
  size: z.enum(['256x256', '512x512', '1024x1024']).default('512x512'),
  quality: z.enum(['standard', 'hd']).default('standard'),
  variants: z.number().min(1).max(4).optional(),
  projectName: z.string().optional(),
});

const styleLookup: { [key: string]: string } = {
  flashy:
    'Flashy, attention grabbing, bold, futuristic, and eye-catching. Use vibrant neon colors with metallic, shiny, and glossy accents.',
  tech: 'highly detailed, sharp focus, cinematic, photorealistic, Minimalist, clean, sleek, neutral color pallete with subtle accents, clean lines, shadows, and flat.',
  corporate:
    'modern, forward-thinking, flat design, geometric shapes, clean lines, natural colors with subtle accents, use strategic negative space to create visual interest.',
  creative:
    'playful, lighthearted, bright bold colors, rounded shapes, lively.',
  abstract:
    'abstract, artistic, creative, unique shapes, patterns, and textures to create a visually interesting and wild logo.',
  minimal:
    'minimal, simple, timeless, versatile, single color logo, use negative space, flat design with minimal details, Light, soft, and subtle.',
};

function resolvePlanAndSubject(sessionClaims: unknown, userId: string) {
  type Claims =
    | { entitlements?: string[]; username?: string }
    | null
    | undefined;
  const claims = sessionClaims as Claims;
  const entitlements = Array.isArray(claims?.entitlements)
    ? (claims?.entitlements as string[])
    : undefined;
  const plan = getUserPlanFromEntitlements(entitlements);
  return { plan, subjectKey: userId } as const;
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const data = FormSchema.parse(body);

    const { userId, sessionClaims } = await auth();

    // Determine plan & limiter
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { plan, subjectKey } = resolvePlanAndSubject(sessionClaims, userId);
    await setRlsUser(userId);

    // Burst limiter only (credits enforce monthly usage)
    const { success: burstOk } = await createFixedWindowLimiter(
      30,
      '1 m',
      'superlogo:burst'
    ).limit(subjectKey);
    if (!burstOk) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 });
    }

    await ensureUserCredits(userId, plan);
    const dec = await decrementCredit(userId);
    if (!dec.success) {
      return NextResponse.json(
        { error: 'Out of credits. Please upgrade.' },
        { status: 402 }
      );
    }

    const client = getOpenAIClient();
    const prompt = dedent`A single logo, high-quality, award-winning professional design, made for both digital and print media, only contains a few vector shapes, ${styleLookup[data.style]}.Primary color is ${data.primaryColor.toLowerCase()} and background color is ${data.secondaryColor.toLowerCase()}. The company name is ${data.companyName}, make sure to include the company name in the logo. ${data.additionalInfo ? `Additional info: ${data.additionalInfo}` : ''}`;

    const variants = Math.max(1, Math.min(4, data.variants ?? 2));
    const response = await client.images.generate({
      model: data.model,
      prompt,
      response_format: 'url',
      size: data.size,
      quality: data.quality,
      n: variants,
    });
    const urls = (response.data ?? [])
      .map((item: { url?: string }) => item.url)
      .filter((u): u is string => Boolean(u));

    // Ensure we always return the requested number of variants.
    // Some providers may ignore n; do bounded retries to fill the gap.
    const missingCount = Math.max(0, variants - urls.length);
    if (missingCount > 0) {
      const extraCalls = Array.from({ length: missingCount }).map(() =>
        client.images.generate({
          model: data.model,
          prompt,
          response_format: 'url',
          size: data.size,
          quality: data.quality,
          n: 1,
        })
      );
      const extraResults = await Promise.all(extraCalls);
      for (const extra of extraResults) {
        const extraUrl = extra.data?.[0]?.url;
        if (extraUrl) {
          urls.push(extraUrl);
        }
      }
    }

    if (userId && urls.length > 0) {
      await db.insert(logosTable).values(
        urls.map((url) => ({
          image_url: url,
          primary_color: data.primaryColor,
          background_color: data.secondaryColor,
          username:
            (sessionClaims as { username?: string } | null | undefined)
              ?.username || 'Anonymous',
          userId,
          projectName: data.projectName || `${data.companyName} Logo`, // Use provided project name or default
          prompt: data.additionalInfo || data.companyName,
          style: data.style,
        }))
      );
    }

    return NextResponse.json({
      urls,
      plan,
    });
  } catch (_err) {
    return NextResponse.json(
      { error: 'Failed to generate logo' },
      { status: 500 }
    );
  }
}
