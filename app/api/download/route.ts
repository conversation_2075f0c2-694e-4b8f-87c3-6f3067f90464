import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import sharp from 'sharp';
import { getUserPlanFromEntitlements } from '@/lib/plan';

export async function POST(req: Request) {
  const { userId, sessionClaims } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const entitlements = Array.isArray(
    (sessionClaims as { entitlements?: string[] } | null | undefined)
      ?.entitlements
  )
    ? ((sessionClaims as { entitlements?: string[] }).entitlements as string[])
    : undefined;
  const plan = getUserPlanFromEntitlements(entitlements);

  const { url } = await req.json();
  if (!url) {
    return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
  }

  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('fetch failed');
    }
    const contentType = response.headers.get('content-type') || 'image/webp';
    const buffer = Buffer.from(await response.arrayBuffer());

    // For free plan, return a watermarked image instead of blocking
    if (plan === 'free') {
      // Render a simple diagonal SVG watermark
      const watermarkSvg = Buffer.from(
        `<svg xmlns='http://www.w3.org/2000/svg' width='1000' height='1000'>
           <defs>
             <linearGradient id='g' x1='0' y1='0' x2='1' y2='1'>
               <stop offset='0%' stop-color='white' stop-opacity='0.0'/>
               <stop offset='50%' stop-color='white' stop-opacity='0.25'/>
               <stop offset='100%' stop-color='white' stop-opacity='0.0'/>
             </linearGradient>
           </defs>
           <g transform='rotate(-30 500 500)'>
             <text x='50' y='520' font-size='90' font-family='Arial, Helvetica, sans-serif' fill='url(#g)' stroke='white' stroke-width='1' opacity='0.6'>
               SUPERLOGO PREVIEW
             </text>
           </g>
         </svg>`
      );

      const input = sharp(buffer).ensureAlpha();
      const meta = await input.metadata();
      const width = meta.width ?? 1024;
      const height = meta.height ?? 1024;

      const watermarked = await input
        .composite([
          {
            input: await sharp(watermarkSvg)
              .resize({
                width: Math.floor(width * 0.9),
                height: Math.floor(height * 0.9),
                fit: 'contain',
              })
              .toBuffer(),
            gravity: 'center',
          },
        ])
        .toFormat('webp')
        .toBuffer();

      const base64 = watermarked.toString('base64');
      return NextResponse.json({ data: `data:image/webp;base64,${base64}` });
    }

    // Paid plans: return original
    const base64Image = buffer.toString('base64');
    return NextResponse.json({
      data: `data:${contentType};base64,${base64Image}`,
    });
  } catch {
    return NextResponse.json(
      { error: 'Failed to download image' },
      { status: 500 }
    );
  }
}
