import crypto from 'node:crypto';
import { NextResponse } from 'next/server';
import { refillCredits } from '@/lib/credits';
import type { Plan } from '@/lib/plan';

function mapEntitlementsToPlan(entitlements?: string[]): Plan {
  if (!entitlements) {
    return 'free';
  }
  if (entitlements.includes('key_elite') || entitlements.includes('elite')) {
    return 'elite';
  }
  if (entitlements.includes('key_pro') || entitlements.includes('pro')) {
    return 'pro';
  }
  if (
    entitlements.includes('key_starter') ||
    entitlements.includes('starter')
  ) {
    return 'starter';
  }
  return 'free';
}

export async function POST(req: Request) {
  try {
    const rawBody = await req.text();
    // Signature verification (HMAC SHA256)
    const secret = process.env.CLERK_WEBHOOK_SECRET;
    if (!secret) {
      return NextResponse.json({ ok: false }, { status: 500 });
    }
    const signature = (await req.headers).get('svix-signature') || '';
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(rawBody, 'utf8');
    const expected = hmac.digest('hex');
    if (!signature.includes(expected)) {
      return NextResponse.json({ ok: false }, { status: 401 });
    }

    const body = JSON.parse(rawBody) as {
      user_id?: string;
      entitlements?: string[];
      type?: string;
    };
    const { user_id, entitlements } = body;
    if (!user_id) {
      return NextResponse.json({ ok: true });
    }
    const plan = mapEntitlementsToPlan(entitlements);
    await refillCredits(user_id, plan);
    return NextResponse.json({ ok: true });
  } catch {
    return NextResponse.json({ ok: false }, { status: 400 });
  }
}
