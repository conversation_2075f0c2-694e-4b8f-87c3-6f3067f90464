import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { setRlsUser } from '@/db';
import { ensureUserCredits, getRemainingCredits } from '@/lib/credits';
import {
  getMonthlyLimitForPlan,
  getUserPlanFromEntitlements,
} from '@/lib/plan';

export async function GET() {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { sessionClaims } = await auth();
  const entitlements = Array.isArray(
    (sessionClaims as { entitlements?: string[] } | null | undefined)
      ?.entitlements
  )
    ? ((sessionClaims as { entitlements?: string[] }).entitlements as string[])
    : undefined;
  const plan = getUserPlanFromEntitlements(entitlements);
  await setRlsUser(userId);
  await ensureUserCredits(userId, plan);
  const remaining = await getRemainingCredits(userId);
  const monthlyLimit = getMonthlyLimitForPlan(plan);
  return NextResponse.json({ remaining, plan, monthlyLimit });
}
