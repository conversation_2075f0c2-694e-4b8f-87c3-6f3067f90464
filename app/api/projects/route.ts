import { auth } from '@clerk/nextjs/server';
import { and, desc, eq, sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';

/**
 * Get all projects for the authenticated user
 * Groups logos by projectName and returns project summaries
 */
export async function GET() {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);

    // Get all projects grouped by projectName
    const projects = await db
      .select({
        id: sql<string>`MIN(${logosTable.id})`.as('id'),
        projectName: logosTable.projectName,
        // Prefer explicit cover image when present; otherwise fall back to most recent non-empty image_url
        thumbnail: sql<string>`(
            array_agg(
              ${logosTable.image_url}
              ORDER BY CASE WHEN (${logosTable.metadata} ->> 'cover') = 'true' THEN 0 ELSE 1 END,
                       ${logosTable.createdAt} DESC
            ) FILTER (WHERE ${logosTable.image_url} != '')
          )[1]`.as('thumbnail'),
        prompt:
          sql<string>`(array_agg(${logosTable.prompt} ORDER BY ${logosTable.createdAt} DESC))[1]`.as(
            'prompt'
          ),
        style:
          sql<string>`(array_agg(${logosTable.style} ORDER BY ${logosTable.createdAt} DESC))[1]`.as(
            'style'
          ),
        primaryColor:
          sql<string>`(array_agg(${logosTable.primary_color} ORDER BY ${logosTable.createdAt} DESC))[1]`.as(
            'primaryColor'
          ),
        backgroundColor:
          sql<string>`(array_agg(${logosTable.background_color} ORDER BY ${logosTable.createdAt} DESC))[1]`.as(
            'backgroundColor'
          ),
        logoCount:
          sql<number>`COUNT(*) FILTER (WHERE ${logosTable.image_url} != '')`.as(
            'logoCount'
          ),
        lastModified: sql<Date>`MAX(${logosTable.createdAt})`.as(
          'lastModified'
        ),
        isFavorite: sql<boolean>`BOOL_OR(${logosTable.isFavorite})`.as(
          'isFavorite'
        ),
        createdAt: sql<Date>`MIN(${logosTable.createdAt})`.as('createdAt'),
      })
      .from(logosTable)
      .where(
        and(eq(logosTable.userId, userId), eq(logosTable.isDeleted, false))
      )
      .groupBy(logosTable.projectName)
      .orderBy(
        desc(sql`BOOL_OR(${logosTable.isFavorite})`),
        desc(sql`MAX(${logosTable.createdAt})`)
      );

    return NextResponse.json(projects);
  } catch (_error) {
    // console.error('Failed to fetch projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}
