'use server';

import { auth } from '@clerk/nextjs/server';
import { and, asc, eq, gte, isNull, lte, ne, or } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';

/**
 * GET /api/projects/:projectName/siblings/:logoId
 *
 * Returns the paired logos (id + url) generated in the same batch as the given logo. This is used
 * by the generator deep-link to preload the exact variants that were produced together
 * with the selected logo, ensuring historical accuracy when viewing older logos.
 *
 * Batch heuristic: rows sharing the same `projectName`, `userId`, `prompt`, `style`,
 * `primary_color`, `background_color`, and created within a short time window
 * around the reference logo's `createdAt` (fallback) or identical timestamp (preferred).
 *
 * Why this approach: we don't have an explicit generation batch ID in the schema yet.
 * Using precise equality on `createdAt` along with the other fields is a pragmatic
 * and reliable grouping for values inserted together in a single request.
 *
 * @param _request - The incoming Next.js Request (unused)
 * @param params - Route params containing `projectName` and `logoId`
 * @returns JSON payload: `{ items: Array<{ id: number; url: string }> }` on success,
 * error JSON with appropriate status on failure
 * @throws 401 if unauthenticated; 404 if the reference logo is not found; 500 on server error
 *
 * @example
 * // Fetch sibling logos for a saved logo id 42
 * fetch('/api/projects/My%20Project/siblings/42')
 *   .then(r => r.json())
 *   .then(({ items }) => console.log(items));
 */
export async function GET(
  _request: Request,
  { params }: { params: Promise<{ projectName: string; logoId: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);

    const { projectName, logoId } = await params;

    const ParamsSchema = z.object({
      projectName: z.string().min(1),
      logoId: z.coerce.number().int().nonnegative(),
    });

    const { projectName: rawProject, logoId: id } = ParamsSchema.parse({
      projectName,
      logoId,
    });

    const decodedProject = decodeURIComponent(rawProject);

    // Find the reference logo
    const [ref] = await db
      .select()
      .from(logosTable)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProject),
          eq(logosTable.id, id),
          eq(logosTable.isDeleted, false)
        )
      )
      .limit(1);

    if (!ref) {
      return NextResponse.json({ error: 'Logo not found' }, { status: 404 });
    }

    // Query siblings by same generation characteristics.
    const conditions = [
      eq(logosTable.userId, userId),
      eq(logosTable.projectName, decodedProject),
      eq(logosTable.isDeleted, false),
      ne(logosTable.image_url, ''),
      eq(logosTable.primary_color, ref.primary_color),
      eq(logosTable.background_color, ref.background_color),
    ];

    if (ref.prompt === null) {
      conditions.push(isNull(logosTable.prompt));
    } else {
      conditions.push(eq(logosTable.prompt, ref.prompt));
    }

    if (ref.style === null) {
      conditions.push(isNull(logosTable.style));
    } else {
      conditions.push(eq(logosTable.style, ref.style));
    }

    // Prefer exact createdAt match; fall back to ±60s time window to handle
    // providers/drivers that might not set identical timestamps for bulk inserts.
    const windowStart = new Date((ref.createdAt as Date).getTime() - 60_000);
    const windowEnd = new Date((ref.createdAt as Date).getTime() + 60_000);

    const siblings = await db
      .select()
      .from(logosTable)
      .where(
        and(
          ...conditions,
          or(
            eq(logosTable.createdAt, ref.createdAt as Date),
            and(
              gte(logosTable.createdAt, windowStart),
              lte(logosTable.createdAt, windowEnd)
            )
          )
        )
      )
      .orderBy(asc(logosTable.id));

    const items = siblings
      .map((s) => ({ id: s.id, url: s.image_url }))
      .filter((i) => i.url && i.url.trim() !== '');

    return NextResponse.json({ items });
  } catch (_err) {
    return NextResponse.json(
      { error: 'Failed to fetch sibling logos' },
      { status: 500 }
    );
  }
}
