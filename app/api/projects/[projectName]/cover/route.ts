import { auth } from '@clerk/nextjs/server';
import { and, eq, sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';

const SetCoverSchema = z.object({
  logoId: z.number().int().positive(),
});

/**
 * POST /api/projects/[projectName]/cover
 * Sets a specific logo as the project's cover image.
 * Ensures only one cover per project by unsetting others.
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ projectName: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);
    const { projectName } = await params;
    const decodedProjectName = decodeURIComponent(projectName);

    const body = await request.json();
    const data = SetCoverSchema.parse(body);

    // Verify the logo belongs to the current user and project
    const [existing] = await db
      .select({ id: logosTable.id })
      .from(logosTable)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName),
          eq(logosTable.id, data.logoId)
        )
      )
      .limit(1);

    if (!existing) {
      return NextResponse.json(
        { error: 'Logo not found for this project' },
        { status: 404 }
      );
    }

    // Unset cover for all logos in this project
    await db
      .update(logosTable)
      .set({
        // jsonb_set(metadata, '{cover}', 'false', true)
        metadata: sql`jsonb_set(coalesce(${logosTable.metadata}, '{}'::jsonb), '{cover}', 'false'::jsonb, true)`,
      })
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName)
        )
      );

    // Set cover on the selected logo
    await db
      .update(logosTable)
      .set({
        metadata: sql`jsonb_set(coalesce(${logosTable.metadata}, '{}'::jsonb), '{cover}', 'true'::jsonb, true)`,
      })
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName),
          eq(logosTable.id, data.logoId)
        )
      );

    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to set cover image' },
      { status: 500 }
    );
  }
}
