'use server';

import { auth } from '@clerk/nextjs/server';
import { and, desc, eq, ne } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';

/**
 * GET /api/projects/[projectName]/generations
 *
 * Returns individual logos for a project, newest first.
 * Rationale: The right sidebar should mirror the project detail page count and
 * allow selecting any logo to load its sibling pair via the siblings API.
 */
export async function GET(
  _req: Request,
  { params }: { params: Promise<{ projectName: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  await setRlsUser(userId);

  const { projectName } = await params;
  const decodedProjectName = decodeURIComponent(projectName);
  const Params = z.object({ projectName: z.string().min(1) });
  const parsed = Params.safeParse({ projectName: decodedProjectName });
  if (!parsed.success) {
    return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
  }

  const rows = await db
    .select()
    .from(logosTable)
    .where(
      and(
        eq(logosTable.projectName, parsed.data.projectName),
        eq(logosTable.userId, userId),
        eq(logosTable.isDeleted, false),
        // Exclude placeholder records created at project creation time
        ne(logosTable.image_url, '')
      )
    )
    .orderBy(desc(logosTable.createdAt));

  const items = rows.map((r) => ({
    id: r.id,
    createdAt: r.createdAt?.toISOString?.() ?? String(r.createdAt ?? ''),
    thumbnailUrl: r.image_url,
    prompt: r.prompt ?? null,
    style: r.style ?? null,
    primary_color: r.primary_color,
    background_color: r.background_color,
  }));

  return NextResponse.json({ items });
}
