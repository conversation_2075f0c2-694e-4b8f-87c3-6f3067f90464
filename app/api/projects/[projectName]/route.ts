import { auth } from '@clerk/nextjs/server';
import { and, desc, eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import { logosTable } from '@/db/schema';

/**
 * Get all logos for a specific project
 */
export async function GET(
  _request: Request,
  { params }: { params: Promise<{ projectName: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);
    const { projectName } = await params;
    const decodedProjectName = decodeURIComponent(projectName);

    const logos = await db
      .select()
      .from(logosTable)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName),
          eq(logosTable.isDeleted, false)
        )
      )
      .orderBy(desc(logosTable.createdAt));

    return NextResponse.json(logos);
  } catch (_error) {
    // console.error('Failed to fetch project logos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project logos' },
      { status: 500 }
    );
  }
}

const UpdateProjectSchema = z.object({
  newName: z.string().min(1).max(100).optional(),
  isFavorite: z.boolean().optional(),
});

/**
 * Update project metadata (rename, favorite)
 */
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ projectName: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);
    const { projectName } = await params;
    const decodedProjectName = decodeURIComponent(projectName);

    const body = await request.json();
    const data = UpdateProjectSchema.parse(body);

    const updateData: Partial<typeof logosTable.$inferInsert> = {};

    if (data.newName) {
      updateData.projectName = data.newName;
    }

    if (typeof data.isFavorite === 'boolean') {
      updateData.isFavorite = data.isFavorite;
    }

    await db
      .update(logosTable)
      .set(updateData)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName)
        )
      );

    return NextResponse.json({ success: true });
  } catch (_error) {
    // console.error('Failed to update project:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

/**
 * Delete project (HARD delete - actually removes from database)
 */
export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ projectName: string }> }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await setRlsUser(userId);
    const { projectName } = await params;
    const decodedProjectName = decodeURIComponent(projectName);

    // HARD DELETE - actually remove from database
    const result = await db
      .delete(logosTable)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, decodedProjectName)
        )
      );

    return NextResponse.json({
      success: true,
      deletedCount: result.rowCount || 0,
    });
  } catch (_error) {
    // console.error('Failed to delete project:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}
