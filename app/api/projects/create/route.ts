import { auth } from '@clerk/nextjs/server';
import { and, eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { db, setRlsUser } from '@/db';
import type { SelectLogo } from '@/db/schema';
import { logosTable } from '@/db/schema';

const CreateProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
});

/**
 * Create a project if it doesn't exist yet; otherwise return the existing one.
 * Ensures unique project names per user (non-deleted).
 *
 * @param req - Request containing JSON body: { name: string }
 * @returns JSON with shape: { success: true, project: { id: number, name: string, isPlaceholder: boolean } }
 * @throws 401 if unauthenticated, 400 for invalid input, 500 on server error
 * @example
 * POST /api/projects/create { "name": "Brand A" }
 */
export async function POST(req: Request) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await req.json();
    const data = CreateProjectSchema.parse(body);

    await setRlsUser(userId);

    // If a non-deleted project with the same name exists, return it
    const existing: SelectLogo[] = await db
      .select()
      .from(logosTable)
      .where(
        and(
          eq(logosTable.userId, userId),
          eq(logosTable.projectName, data.name),
          eq(logosTable.isDeleted, false)
        )
      );

    if (existing.length > 0) {
      const meta = (existing[0].metadata ?? null) as Record<
        string,
        unknown
      > | null;
      const isPlaceholder = Boolean(meta && meta.isPlaceholder === true);
      return NextResponse.json({
        success: true,
        project: {
          id: existing[0].id,
          name: existing[0].projectName,
          isPlaceholder,
        },
      });
    }

    // Create a project placeholder by inserting a special record
    // This will show up in the projects list even without any actual logos
    const [project] = await db
      .insert(logosTable)
      .values({
        projectName: data.name,
        userId,
        username: 'placeholder', // Temporary placeholder
        image_url: '', // Empty placeholder
        primary_color: '#000000', // Default placeholder
        background_color: '#FFFFFF', // Default placeholder
        prompt: '', // No prompt yet
        style: 'minimal', // Default style
        isDeleted: false,
        isFavorite: false,
        editCount: 0,
        metadata: { isPlaceholder: true }, // Mark as placeholder
      })
      .returning();

    return NextResponse.json({
      success: true,
      project: {
        id: project.id,
        name: project.projectName,
        isPlaceholder: true,
      },
    });
  } catch (error) {
    // console.error('Failed to create project:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
