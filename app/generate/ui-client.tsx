/**
 * Revolutionary Chat-First Logo Generation Client
 * AI-powered conversational design creation with multi-variant preview
 */

'use client';

import { AssistantRuntimeProvider } from '@assistant-ui/react';
import { useChatRuntime } from '@assistant-ui/react-ai-sdk';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { GenerateComposer } from '@/components/assistant-ui/generate-composer';
import { useToast } from '@/hooks/use-toast';
import type { Plan } from '@/lib/plan';
import { GenerationLayout } from './components/generation-layout';
import { type HistoryItem, HistorySidebar } from './components/history-sidebar';
import { MultiVariantPreview } from './components/multi-variant-preview';
import { SettingsSidebar } from './components/settings-sidebar';
import type { ModelType, QualityType, SizeType } from './types';

// Compiled once at module scope to avoid recreating the regex per call.
const HEX_COLOR_RE = /^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/;
const isValidHex = (value: string): boolean => HEX_COLOR_RE.test(value.trim());

/**
 * Fetch sibling logos generated in the same batch.
 * Returns items with DB id and URL so we can preserve original order
 * and mark the clicked one as selected without reordering the tray.
 *
 * @param project - Project name
 * @param logoId - Reference logo id
 * @returns Array of sibling items: `{ id, url }`; empty on error
 */
interface SiblingItem {
  id: number;
  url: string;
}
async function fetchSiblingUrls(
  project: string,
  logoId: string
): Promise<SiblingItem[]> {
  const url = `/api/projects/${encodeURIComponent(project)}/siblings/${encodeURIComponent(
    logoId
  )}`;
  const res = await fetch(url);
  if (!res.ok) {
    return [];
  }
  const data = (await res.json()) as {
    items?: Array<{ id: unknown; url: unknown }>;
  };
  const items = Array.isArray(data.items)
    ? data.items
        .map((it) => ({ id: Number(it.id), url: String(it.url ?? '') }))
        .filter((it) => Number.isFinite(it.id) && it.url.trim().length > 0)
    : [];
  return items;
}

interface LogoVariant {
  id: string;
  url: string;
  isPrimary?: boolean;
  liked?: boolean;
  // Database id of the persisted logo (when known). Used to preserve selection across history.
  dbId?: number;
}

// History feature removed: UI is simplified to preview + chat only.

/**
 * Convert sibling API items into UI variants while preserving original order
 * and marking the provided db id as primary.
 *
 * @param items - Sibling items from API
 * @param primaryDbId - The db id that should be primary in preview
 * @returns Exactly two variants if available; ensures one primary flag
 */
function itemsToVariants(
  items: SiblingItem[],
  primaryDbId: number
): LogoVariant[] {
  const ts = Date.now();
  const mapped = items.slice(0, 2).map((it, i) => ({
    id: `${ts}-${i}`,
    url: it.url,
    dbId: it.id,
    isPrimary: it.id === primaryDbId,
    liked: false,
  }));
  if (mapped.some((m) => m.isPrimary)) {
    return mapped;
  }
  return mapped.map((m, i) => ({ ...m, isPrimary: i === 0 }));
}

/**
 * Build a basic two-tile variant fallback from history when siblings are missing.
 *
 * @param history - Current history items list
 * @param id - Selected history id
 * @returns Two duplicated tiles from thumbnail or null if unavailable
 */
function fallbackVariantsFromHistory(
  history: HistoryItem[],
  id: number
): LogoVariant[] | null {
  const selected = history.find((h) => h.id === id);
  if (!selected?.thumbnailUrl) {
    return null;
  }
  const ts = Date.now();
  return [
    {
      id: `${ts}-a`,
      url: selected.thumbnailUrl,
      isPrimary: true,
      liked: false,
    },
    {
      id: `${ts}-b`,
      url: selected.thumbnailUrl,
      isPrimary: false,
      liked: false,
    },
  ];
}

/**
 * Revolutionary chat-first logo generation interface
 * @returns JSX element for the complete logo generation interface
 */
export default function ClientGenerate() {
  // Project context from URL parameters
  const searchParams = useSearchParams();
  const projectName = searchParams.get('project');
  const initialPrompt = searchParams.get('prompt') ?? undefined;
  const initialStyle = searchParams.get('style') ?? undefined;
  const initialPrimary = searchParams.get('primaryColor') ?? undefined;
  const initialBackground = searchParams.get('backgroundColor') ?? undefined;
  const initialImageUrl = searchParams.get('imageUrl') ?? undefined;
  const initialLogoId = searchParams.get('logoId') ?? undefined;

  // Settings state (simplified - no more form steps)
  const [selectedStyle, setSelectedStyle] = useState('minimal');
  const [primaryColor, setPrimaryColor] = useState('#2563EB');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');
  const [selectedModel, setSelectedModel] = useState<ModelType>(
    'black-forest-labs/flux-schnell'
  );

  const [selectedSize, setSelectedSize] = useState<SizeType>('512x512');
  const [selectedQuality, setSelectedQuality] =
    useState<QualityType>('standard');

  // Generation state
  const [loading, setLoading] = useState(false);
  const [variants, setVariants] = useState<LogoVariant[]>([]);
  const [credits, setCredits] = useState<number | null>(null);
  const [_planName, setPlanName] = useState<Plan | null>(null);
  const [_monthlyLimit, setMonthlyLimit] = useState<number | null>(null);
  // History feature removed
  const [hydrated, setHydrated] = useState(false);
  const [restoredFromStorage, setRestoredFromStorage] = useState(false);
  // Project history sidebar state
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [selectedHistoryId, setSelectedHistoryId] = useState<number | null>(
    null
  );

  /**
   * Build a stable localStorage key for persisting generator state per project.
   * Using a project-scoped key keeps states isolated when switching projects.
   * @param project - Optional project name from query params
   * @returns Namespaced storage key
   * @example
   * const key = makeStateKey('acme'); // "superlogo:genstate:acme"
   */
  const makeStateKey = useCallback((project?: string | null) => {
    return `superlogo:genstate:${project ?? 'default'}`;
  }, []);

  interface GenerationState {
    variants: LogoVariant[];
    selectedStyle: string;
    primaryColor: string;
    backgroundColor: string;
    selectedModel: ModelType;
    selectedSize: SizeType;
    selectedQuality: QualityType;
    prompt?: string;
    ts: number;
  }

  const { toast } = useToast();
  const runtime = useChatRuntime({
    api: '/api/chat',
  } as unknown as Record<string, unknown>);

  // 1) Restore from localStorage by project on mount
  // If an explicit imageUrl or logoId is present, we intentionally skip restoring
  // variants from storage so the deep-linked content always wins.
  useEffect(() => {
    try {
      if (initialImageUrl || initialLogoId) {
        return;
      }
      const raw = localStorage.getItem(makeStateKey(projectName));
      if (raw) {
        const parsed = JSON.parse(raw) as GenerationState;
        if (Array.isArray(parsed.variants) && parsed.variants.length > 0) {
          setSelectedStyle(parsed.selectedStyle);
          setPrimaryColor(parsed.primaryColor);
          setBackgroundColor(parsed.backgroundColor);
          setSelectedModel(parsed.selectedModel);
          setSelectedSize(parsed.selectedSize);
          setSelectedQuality(parsed.selectedQuality);
          setVariants(parsed.variants);
          setRestoredFromStorage(true);
        }
      }
    } catch {
      // ignore corrupt cache
    } finally {
      setHydrated(true);
    }
  }, [makeStateKey, projectName, initialImageUrl, initialLogoId]);

  // 2) Initialize from URL params if not restored from storage
  useEffect(() => {
    if (!hydrated || restoredFromStorage) {
      return;
    }
    if (initialStyle) {
      setSelectedStyle(initialStyle);
    }
    if (initialPrimary && isValidHex(initialPrimary)) {
      setPrimaryColor(initialPrimary);
    }
    if (initialBackground && isValidHex(initialBackground)) {
      setBackgroundColor(initialBackground);
    }
  }, [
    hydrated,
    restoredFromStorage,
    initialStyle,
    initialPrimary,
    initialBackground,
  ]);

  // 3a) If opened via logoId (deep-link from a specific saved logo) and no storage, fetch siblings
  useEffect(() => {
    const shouldFetchSiblings =
      hydrated &&
      !restoredFromStorage &&
      Boolean(projectName) &&
      Boolean(initialLogoId) &&
      variants.length === 0;

    if (!shouldFetchSiblings) {
      return;
    }

    let cancelled = false;
    (async () => {
      try {
        const items = await fetchSiblingUrls(
          String(projectName),
          String(initialLogoId)
        );
        if (cancelled || items.length === 0) {
          return;
        }
        const ts = Date.now();
        // Keep original order from API; mark the deep-linked one as primary
        const clickedId = Number(initialLogoId);
        const mapped = items.slice(0, 2).map((it, i) => ({
          id: `${ts}-${i}`,
          url: it.url,
          dbId: it.id,
          isPrimary: it.id === clickedId,
          liked: false,
        }));
        // Ensure at least one primary
        const hasPrimary = mapped.some((m) => m.isPrimary);
        setVariants(
          hasPrimary
            ? mapped
            : mapped.map((m, i) => ({ ...m, isPrimary: i === 0 }))
        );
      } catch {
        // silent fallback to imageUrl effect below
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [
    hydrated,
    restoredFromStorage,
    projectName,
    initialLogoId,
    variants.length,
  ]);

  // 3b) If opened from a specific logo image and no storage, prefill variants
  useEffect(() => {
    if (!hydrated || restoredFromStorage) {
      return;
    }
    if (initialImageUrl && !variants.length) {
      const ts = Date.now();
      setVariants([
        { id: `${ts}-a`, url: initialImageUrl, isPrimary: true, liked: false },
        { id: `${ts}-b`, url: initialImageUrl, isPrimary: false, liked: false },
      ]);
    }
  }, [hydrated, restoredFromStorage, initialImageUrl, variants.length]);

  /**
   * Fetch project history (generation batches) for the right sidebar.
   */
  const fetchHistory = useCallback(async (): Promise<HistoryItem[]> => {
    if (!projectName) {
      setHistory([]);
      return [];
    }
    try {
      const res = await fetch(
        `/api/projects/${encodeURIComponent(projectName)}/generations`
      );
      if (!res.ok) {
        setHistory([]);
        return [];
      }
      const data = (await res.json()) as {
        items?: Array<
          HistoryItem & {
            style?: string | null;
            primary_color?: string;
            background_color?: string;
          }
        >;
      };
      const items = Array.isArray(data.items) ? data.items : [];
      const mapped: HistoryItem[] = items.map((it) => ({
        id: it.id,
        createdAt: it.createdAt,
        prompt: it.prompt ?? null,
        thumbnailUrl: it.thumbnailUrl,
      }));
      setHistory(mapped);
      if (mapped.length === 0) {
        setSelectedHistoryId(null);
      }
      return mapped;
    } catch {
      setHistory([]);
      setSelectedHistoryId(null);
      return [];
    }
  }, [projectName]);

  // Load history on mount/project change
  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  /**
   * Selecting a history item loads its sibling variants and updates settings
   * based on the server-reported batch metadata.
   */
  const handleSelectHistory = useCallback(
    async (id: number) => {
      if (!projectName) {
        return;
      }
      // Optimistic selection to provide immediate UI feedback.
      setSelectedHistoryId(id);
      try {
        const items = await fetchSiblingUrls(String(projectName), String(id));
        if (items.length > 0) {
          setVariants(itemsToVariants(items, id));
          return;
        }

        // Fallback: if no siblings found (e.g., heuristic miss), use the
        // selected history thumbnail so the preview still updates.
        const fb = fallbackVariantsFromHistory(history, id);
        if (fb) {
          setVariants(fb);
          setSelectedHistoryId(id);
          toast({
            title: 'Loaded single image',
            description: 'Could not find the sibling pair for this item.',
          });
        }
      } catch (err) {
        const fb = fallbackVariantsFromHistory(history, id);
        if (fb) {
          setVariants(fb);
          setSelectedHistoryId(id);
        }
        toast({
          title: 'Unable to switch preview',
          description:
            err instanceof Error
              ? err.message
              : 'Failed to load sibling images.',
          variant: 'destructive',
        });
      }
    },
    [projectName, history, toast]
  );

  // 3c) If opened with only project (no deep-link, no storage), auto-select latest history
  useEffect(() => {
    const shouldAutoSelect =
      hydrated &&
      !restoredFromStorage &&
      !initialImageUrl &&
      !initialLogoId &&
      variants.length === 0 &&
      history.length > 0 &&
      selectedHistoryId == null;
    if (!shouldAutoSelect) {
      return;
    }
    const first = history[0];
    if (!first) {
      return;
    }
    (async () => {
      await handleSelectHistory(first.id);
    })();
  }, [
    hydrated,
    restoredFromStorage,
    initialImageUrl,
    initialLogoId,
    variants.length,
    history,
    selectedHistoryId,
    handleSelectHistory,
  ]);

  /**
   * Fetch user's remaining credits from API
   */
  const fetchCredits = useCallback(async () => {
    const res = await fetch('/api/credits');
    if (!res.ok) {
      return;
    }
    const data = (await res.json()) as {
      remaining: number;
      plan?: Plan;
      monthlyLimit?: number;
    };
    const nextCredits = typeof data.remaining === 'number' ? data.remaining : 0;
    setCredits(nextCredits);
    if (data.plan) {
      setPlanName(data.plan);
    }
    if (typeof data.monthlyLimit === 'number') {
      setMonthlyLimit(data.monthlyLimit);
    }

    // broadcast + cache
    window.dispatchEvent(
      new CustomEvent('credits:update', {
        detail: {
          credits: nextCredits,
          monthlyLimit:
            typeof data.monthlyLimit === 'number' ? data.monthlyLimit : null,
          plan: data.plan ?? null,
        },
      })
    );
    localStorage.setItem(
      'superlogo:credits:last',
      JSON.stringify({
        credits: nextCredits,
        monthlyLimit:
          typeof data.monthlyLimit === 'number' ? data.monthlyLimit : null,
        plan: data.plan ?? null,
        ts: Date.now(),
      })
    );
  }, []);

  /**
   * Handle instruction-based logo generation
   */
  const sendGenerate = useCallback(async (payload: Record<string, unknown>) => {
    const res = await fetch('/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
    return res;
  }, []);

  const handleGenerate = useCallback(
    async (prompt: string) => {
      if (!prompt.trim()) {
        return;
      }

      setLoading(true);
      try {
        const res = await sendGenerate({
          companyName: prompt,
          style: selectedStyle,
          symbolPreference: 'modern and professional',
          primaryColor,
          secondaryColor: backgroundColor,
          model: selectedModel,
          size: selectedSize,
          quality: selectedQuality,
          variants: 2,
          additionalInfo: prompt,
          projectName: projectName || undefined,
        });

        if (res.status === 402) {
          toast({
            title: 'Upgrade required',
            description: 'You are out of credits. Please upgrade to continue.',
            variant: 'destructive',
          });
          return;
        }

        const { urls = [] } = (await res.json()) as { urls?: string[] };
        const successfulResults = urls.map((url, index) => ({
          id: `${Date.now()}-${index}`,
          url,
          isPrimary: index === 0,
          liked: false,
        }));

        if (successfulResults.length > 0) {
          const newVariants: LogoVariant[] = successfulResults.map(
            (result, index) => ({
              id: `${Date.now()}-${index}`,
              url: result.url,
              isPrimary: index === 0,
              liked: false,
            })
          );

          // Always display the first image as the large preview; duplicate
          // it into the variant tray as the first variant so switching works
          // like competitor UX.
          setVariants(() => {
            const uniqueUrls = Array.from(
              new Set(newVariants.map((v) => v.url))
            );
            const firstUrl = uniqueUrls[0] ?? newVariants[0]?.url ?? '';
            const secondUrl = uniqueUrls[1] ?? firstUrl;
            const ts = Date.now();
            return [
              { id: `${ts}-a`, url: firstUrl, isPrimary: true, liked: false },
              { id: `${ts}-b`, url: secondUrl, isPrimary: false, liked: false },
            ];
          });
          // History feature removed

          // Refresh credits and history so the sidebar reflects the new batch.
          await fetchCredits();
          const updatedHistory = await fetchHistory();
          if (updatedHistory.length > 0) {
            const newest = updatedHistory[0];
            setSelectedHistoryId(newest.id);
            await handleSelectHistory(newest.id);
          }
          toast({
            title: 'Success!',
            description: `Generated ${newVariants.length} logo variants`,
            variant: 'default',
          });
        } else {
          throw new Error('Failed to generate any logo variants');
        }
      } catch (error) {
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Unexpected error',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    },
    [
      selectedStyle,
      primaryColor,
      backgroundColor,
      selectedModel,
      selectedSize,
      selectedQuality,
      toast,
      fetchCredits,
      fetchHistory,
      sendGenerate,
      projectName,
      handleSelectHistory,
    ]
  );

  /**
   * Handle variant download
   */
  const handleDownload = useCallback(
    async (variant: LogoVariant) => {
      try {
        const res = await fetch('/api/download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url: variant.url }),
        });

        const result = await res.json();

        if (res.ok && result.data) {
          const a = document.createElement('a');
          a.href = result.data;
          a.download = `logo-${variant.id}.webp`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          toast({
            title: 'Download started',
            description: 'Your logo is being downloaded',
          });
        } else {
          throw new Error('Failed to download logo');
        }
      } catch (error) {
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Unexpected error',
          variant: 'destructive',
        });
      }
    },
    [toast]
  );

  /**
   * Handle variant selection as primary
   */
  const handleSelectPrimary = useCallback((variant: LogoVariant) => {
    // Simple toggle: keep both tiles as-is; only flip which one is primary
    setVariants((prev) =>
      prev.map((v) => ({ ...v, isPrimary: v.id === variant.id }))
    );
  }, []);

  /**
   * Handle variant like toggle
   */
  const handleToggleLike = useCallback((variantId: string) => {
    setVariants((prev) =>
      prev.map((v) => (v.id === variantId ? { ...v, liked: !v.liked } : v))
    );
  }, []);

  /**
   * Handle variant refinement
   */
  const handleRefineVariant = useCallback(
    (_variant: LogoVariant) => {
      // Trigger new generation based on this variant
      handleGenerate('Refine this design with improvements');
    },
    [handleGenerate]
  );

  /**
   * Handle regenerate all variants
   */
  const handleRegenerate = useCallback(() => {
    // Just regenerate with the same settings
    handleGenerate('Generate new logo variants');
  }, [handleGenerate]);

  // History feature removed

  // 4) Persist state to localStorage whenever variants or key settings change
  useEffect(() => {
    if (!hydrated) {
      return;
    }
    const key = makeStateKey(projectName);
    const state: GenerationState = {
      variants,
      selectedStyle,
      primaryColor,
      backgroundColor,
      selectedModel,
      selectedSize,
      selectedQuality,
      prompt: initialPrompt,
      ts: Date.now(),
    };
    try {
      if (variants.length > 0) {
        localStorage.setItem(key, JSON.stringify(state));
      }
    } catch {
      // ignore quota errors
    }
  }, [
    hydrated,
    variants,
    selectedStyle,
    primaryColor,
    backgroundColor,
    selectedModel,
    selectedSize,
    selectedQuality,
    makeStateKey,
    projectName,
    initialPrompt,
  ]);

  // Fetch credits on component mount
  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  return (
    <GenerationLayout
      chatPanel={
        <AssistantRuntimeProvider runtime={runtime}>
          <GenerateComposer
            initialPrompt={initialPrompt}
            isGenerating={loading}
            onChangeModel={setSelectedModel}
            onChangeQuality={setSelectedQuality}
            onChangeSize={setSelectedSize}
            onGenerate={handleGenerate}
            selectedModel={selectedModel}
            selectedQuality={selectedQuality}
            selectedSize={selectedSize}
          />
        </AssistantRuntimeProvider>
      }
      credits={credits}
      generatedLogos={variants.map((v) => v.url)}
      previewPanel={
        <MultiVariantPreview
          backgroundColor={backgroundColor}
          loading={loading}
          onDownload={handleDownload}
          onRefineVariant={handleRefineVariant}
          onRegenerate={handleRegenerate}
          onSelectPrimary={handleSelectPrimary}
          onToggleLike={handleToggleLike}
          variants={variants}
        />
      }
      projectName={projectName}
      rightSidebar={
        <HistorySidebar
          items={history}
          onSelect={handleSelectHistory}
          selectedId={selectedHistoryId}
        />
      }
    >
      <SettingsSidebar
        backgroundColor={backgroundColor}
        primaryColor={primaryColor}
        selectedStyle={selectedStyle}
        setBackgroundColor={setBackgroundColor}
        setPrimaryColor={setPrimaryColor}
        setSelectedStyle={setSelectedStyle}
      />
    </GenerationLayout>
  );
}
