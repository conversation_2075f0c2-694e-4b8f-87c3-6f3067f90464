/**
 * Constants and configuration data for logo generation
 */

import {
  IconBolt,
  IconBulb,
  IconComponents,
  IconCube,
  IconEdit,
  IconFlame,
  IconMinimize,
  IconPalette,
  IconSettings,
  IconSparkles,
} from '@tabler/icons-react';
import type {
  ColorOption,
  ModelOption,
  SizeOption,
  StepConfig,
  StyleOption,
} from '../types';

export const STYLE_OPTIONS: StyleOption[] = [
  {
    id: 'minimal',
    name: 'Minimal',
    icon: IconMinimize,
    description: 'Clean, simple design with negative space',
    gradient: 'from-slate-400 to-slate-600',
  },
  {
    id: 'tech',
    name: 'Technology',
    icon: IconBolt,
    description: 'Modern, sharp, geometric tech-inspired',
    gradient: 'from-blue-400 to-cyan-600',
  },
  {
    id: 'corporate',
    name: 'Corporate',
    icon: IconComponents,
    description: 'Professional, trustworthy business design',
    gradient: 'from-emerald-400 to-teal-600',
  },
  {
    id: 'creative',
    name: 'Creative',
    icon: IconBulb,
    description: 'Playful, artistic, bright and unique',
    gradient: 'from-purple-400 to-pink-600',
  },
  {
    id: 'abstract',
    name: 'Abstract',
    icon: IconCube,
    description: 'Artistic patterns and compositions',
    gradient: 'from-orange-400 to-red-600',
  },
  {
    id: 'flashy',
    name: 'Flashy',
    icon: IconFlame,
    description: 'Bold, vibrant, eye-catching design',
    gradient: 'from-yellow-400 to-orange-600',
  },
];

export const MODEL_OPTIONS: ModelOption[] = [
  {
    id: 'stability-ai/sdxl',
    name: 'Stability AI SDXL',
    description: 'High-quality, stable diffusion model',
    badge: 'Popular',
    speed: 'Medium',
  },
  {
    id: 'black-forest-labs/flux-schnell',
    name: 'Flux Schnell',
    description: 'Fastest generation with great quality',
    badge: 'Fastest',
    speed: 'Fast',
  },
  {
    id: 'black-forest-labs/flux-dev',
    name: 'Flux Dev',
    description: 'Premium model for best results',
    badge: 'Premium',
    speed: 'Slow',
  },
];

export const SIZE_OPTIONS: SizeOption[] = [
  { id: '256x256', name: 'Small (256x256)' },
  { id: '512x512', name: 'Medium (512x512)' },
  { id: '1024x1024', name: 'Large (1024x1024)' },
];

export const COLOR_OPTIONS: ColorOption[] = [
  { id: '#2563EB', name: 'Blue' },
  { id: '#DC2626', name: 'Red' },
  { id: '#D97706', name: 'Orange' },
  { id: '#16A34A', name: 'Green' },
  { id: '#9333EA', name: 'Purple' },
  { id: '#000000', name: 'Black' },
];

export const BACKGROUND_OPTIONS: ColorOption[] = [
  { id: '#FFFFFF', name: 'White' },
  { id: '#F8FAFC', name: 'Light Gray' },
  { id: '#FEE2E2', name: 'Light Red' },
  { id: '#000000', name: 'Black' },
  { id: '#FEF2F2', name: 'Light Red' },
  { id: '#EFF6FF', name: 'Light Blue' },
  { id: '#F0FFF4', name: 'Light Green' },
];

export const GENERATION_STEPS: StepConfig[] = [
  { id: 1, title: 'Brand Details', icon: IconEdit },
  { id: 2, title: 'Style & Colors', icon: IconPalette },
  { id: 3, title: 'AI Settings', icon: IconSettings },
  { id: 4, title: 'Generate', icon: IconSparkles },
];
