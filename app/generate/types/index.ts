/**
 * TypeScript interfaces and types for the logo generation feature
 */

export interface StyleOption {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  gradient: string;
}

export interface ModelOption {
  id: string;
  name: string;
  description: string;
  badge: string;
  speed: string;
}

export interface SizeOption {
  id: string;
  name: string;
}

export interface ColorOption {
  id: string;
  name: string;
}

export interface StepConfig {
  id: number;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
}

export type ModelType =
  | 'stability-ai/sdxl'
  | 'dall-e-3'
  | 'black-forest-labs/flux-schnell'
  | 'black-forest-labs/flux-dev';

export type SizeType = '256x256' | '512x512' | '1024x1024';

export type QualityType = 'standard' | 'hd';

export interface GenerateFormData {
  companyName: string;
  selectedStyle: string;
  primaryColor: string;
  backgroundColor: string;
  additionalInfo: string;
  selectedModel: ModelType;
  selectedSize: SizeType;
  selectedQuality: QualityType;
}

export interface StepProps {
  onNext: () => void;
  onPrev?: () => void;
}

export interface StepOneProps extends StepProps {
  companyName: string;
  setCompanyName: (value: string) => void;
  additionalInfo: string;
  setAdditionalInfo: (value: string) => void;
}

export interface StepTwoProps extends StepProps {
  selectedStyle: string;
  setSelectedStyle: (value: string) => void;
  primaryColor: string;
  setPrimaryColor: (value: string) => void;
  backgroundColor: string;
  setBackgroundColor: (value: string) => void;
}

export interface StepThreeProps extends StepProps {
  selectedModel: ModelType;
  setSelectedModel: (value: ModelType) => void;
  selectedSize: SizeType;
  setSelectedSize: (value: SizeType) => void;
  selectedQuality: QualityType;
  setSelectedQuality: (value: QualityType) => void;
}

export interface StepFourProps extends Omit<StepProps, 'onNext'> {
  isFormValid: boolean;
  loading: boolean;
  onGenerate: () => void;
}

export interface CreditsBannerProps {
  remaining: number | null;
  expanded: boolean;
}

export interface LogoPreviewProps {
  generatedLogo: string;
  backgroundColor: string;
  loading: boolean;
  onDownload: () => void;
  onRegenerate: () => void;
}
