/**
 * Multi-Variant Logo Preview - Shows multiple logo variations
 * Inspired by competitor's variant system
 */

'use client';

import {
  IconColorFilter,
  IconDownload,
  IconHeart,
  IconRefresh,
  IconSparkles,
  IconZoomIn,
  IconZoomOut,
  IconZoomReset,
} from '@tabler/icons-react';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface LogoVariant {
  id: string;
  url: string;
  isPrimary?: boolean;
  liked?: boolean;
}

interface MultiVariantPreviewProps {
  variants: LogoVariant[];
  backgroundColor: string;
  loading: boolean;
  onDownload: (variant: LogoVariant) => void;
  onRegenerate: () => void;
  onSelectPrimary: (variant: LogoVariant) => void;
  onToggleLike: (variantId: string) => void;
  onRefineVariant: (variant: LogoVariant) => void;
}

/**
 * Multi-variant logo preview with primary + secondary variants
 * @param variants - Array of logo variants
 * @param backgroundColor - Background color for logo display
 * @param loading - Whether logo generation is in progress
 * @param onDownload - Download callback for specific variant
 * @param onRegenerate - Regenerate all variants
 * @param onSelectPrimary - Select variant as primary
 * @param onToggleLike - Toggle like status
 * @param onRefineVariant - Refine specific variant
 * @returns JSX element showing multi-variant preview
 */
export function MultiVariantPreview(props: MultiVariantPreviewProps) {
  const {
    variants,
    backgroundColor,
    loading,
    onDownload,
    onRegenerate,
    onSelectPrimary,
    onToggleLike,
    onRefineVariant,
  } = props;
  const [zoomLevel, setZoomLevel] = useState(1);

  const primaryVariant = variants.find((v) => v.isPrimary) || variants[0];
  const trayVariants = variants.slice(0, 2);

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 0.25, 0.5));
  };

  const handleZoomFit = () => {
    setZoomLevel(1);
  };

  // Keyboard shortcuts: 1/2 to switch variant, +/- or ZX to zoom, R to reset
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement | null;
      const tag = target?.tagName?.toLowerCase();
      if (tag === 'input' || tag === 'textarea' || target?.isContentEditable) {
        return;
      }
      const key = e.key.toLowerCase();
      const actions: Record<string, () => void> = {
        '1': () => variants[0] && onSelectPrimary(variants[0]),
        '2': () => variants[1] && onSelectPrimary(variants[1]),
        '+': () => setZoomLevel((p) => Math.min(p + 0.25, 3)),
        '=': () => setZoomLevel((p) => Math.min(p + 0.25, 3)),
        x: () => setZoomLevel((p) => Math.min(p + 0.25, 3)),
        '-': () => setZoomLevel((p) => Math.max(p - 0.25, 0.5)),
        z: () => setZoomLevel((p) => Math.max(p - 0.25, 0.5)),
        r: () => setZoomLevel(1),
      };
      const action = actions[key];
      if (action) {
        action();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [onSelectPrimary, variants]);

  if (loading) {
    return <LoadingState />;
  }

  if (variants.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="flex h-full flex-col gap-2">
      {/* Minimal Top Header */}
      <div className="flex items-center justify-between pb-4">
        <div className="flex items-center gap-3">
          {primaryVariant && (
            <Badge
              className="h-6 gap-1 bg-primary/10 px-2 text-primary text-xs"
              variant="outline"
            >
              <IconSparkles className="h-3 w-3" />
              Primary
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <div className="flex items-center gap-1">
            <Button
              disabled={zoomLevel <= 0.5}
              onClick={handleZoomOut}
              size="sm"
              variant="ghost"
            >
              <IconZoomOut className="h-4 w-4" />
            </Button>
            <Button
              disabled={zoomLevel === 1}
              onClick={handleZoomFit}
              size="sm"
              title="Fit to canvas"
              variant="ghost"
            >
              <IconZoomReset className="h-4 w-4" />
            </Button>
            <span className="min-w-12 text-center text-muted-foreground text-xs">
              {Math.round(zoomLevel * 100)}%
            </span>
            <Button
              disabled={zoomLevel >= 3}
              onClick={handleZoomIn}
              size="sm"
              variant="ghost"
            >
              <IconZoomIn className="h-4 w-4" />
            </Button>
          </div>
          <Button
            onClick={() => onToggleLike(primaryVariant.id)}
            size="sm"
            variant="ghost"
          >
            <IconHeart
              className={`h-4 w-4 ${primaryVariant.liked ? 'fill-current text-red-500' : ''}`}
            />
          </Button>
        </div>
      </div>

      {/* Extra Large Main Preview Canvas - Slightly higher */}
      <div className="mt-[-8px] mb-4 flex justify-center">
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className="group relative"
          initial={{ opacity: 0, scale: 0.95 }}
        >
          <div
            className="relative flex h-[560px] w-[560px] items-center justify-center overflow-hidden rounded-3xl"
            style={{ backgroundColor }}
          >
            <div
              className="flex h-full w-full items-center justify-center transition-transform duration-200 ease-in-out"
              style={{
                transform: `scale(${zoomLevel})`,
              }}
            >
              {primaryVariant.url && primaryVariant.url.trim() !== '' && (
                <Image
                  alt="Primary logo variant"
                  className="object-contain"
                  fill
                  src={primaryVariant.url}
                />
              )}
            </div>

            {/* Corner Action Icons - Only visible on hover */}
            <div className="absolute top-4 left-4 opacity-0 transition-opacity group-hover:opacity-100">
              <button
                className="flex h-10 w-10 items-center justify-center rounded-full bg-background/90 shadow-md backdrop-blur-sm transition-all hover:scale-110 hover:bg-background"
                onClick={() => onDownload(primaryVariant)}
                title="Download logo"
                type="button"
              >
                <IconDownload className="h-4 w-4" />
              </button>
            </div>

            <div className="absolute top-4 right-4 opacity-0 transition-opacity group-hover:opacity-100">
              <button
                className="flex h-10 w-10 items-center justify-center rounded-full bg-background/90 shadow-md backdrop-blur-sm transition-all hover:scale-110 hover:bg-background"
                onClick={onRegenerate}
                title="Regenerate all variants"
                type="button"
              >
                <IconRefresh className="h-4 w-4" />
              </button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Refine Button */}
      <div className="mb-6 flex justify-center">
        <Button
          onClick={() => onRefineVariant(primaryVariant)}
          size="sm"
          variant="ghost"
        >
          <IconSparkles className="mr-2 h-4 w-4" />
          Refine this design
        </Button>
      </div>

      {/* Variants Section - Always show both tiles */}
      <AnimatePresence>
        {trayVariants.length > 0 && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="border-border/10 border-t pt-6"
            initial={{ opacity: 0, y: 10 }}
          >
            <p className="mb-4 text-center text-muted-foreground text-sm">
              Variants
            </p>
            <div
              aria-label="Variants"
              className="flex justify-center gap-4"
              role="tablist"
            >
              {trayVariants.map((variant, index) => (
                <motion.button
                  animate={{ opacity: 1, scale: 1 }}
                  aria-selected={variant.isPrimary}
                  className={`relative overflow-hidden rounded-xl border bg-card transition-all hover:scale-105 hover:border-primary hover:shadow-md ${variant.isPrimary ? 'ring-2 ring-primary' : 'opacity-90'}`}
                  initial={{ opacity: 0, scale: 0.95 }}
                  key={variant.id}
                  onClick={() => onSelectPrimary(variant)}
                  role="tab"
                  transition={{ delay: index * 0.05 }}
                  type="button"
                >
                  <div
                    className="h-24 w-24 overflow-hidden rounded-xl"
                    style={{ backgroundColor }}
                  >
                    {variant.url && variant.url.trim() !== '' && (
                      <Image
                        alt={`Variant ${index + 1}`}
                        className="h-full w-full object-contain"
                        height={96}
                        src={variant.url}
                        width={96}
                      />
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/**
 * Loading state component for logo generation
 * @returns JSX element showing loading spinner and message
 */
function LoadingState() {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="flex flex-1 items-center justify-center"
      initial={{ opacity: 0 }}
    >
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: 'linear',
            }}
          >
            <IconSparkles className="h-5 w-5 text-primary" />
          </motion.div>
        </div>
        <div>
          <h4 className="font-medium text-sm">Creating your logo...</h4>
          <p className="mt-1 text-muted-foreground text-xs">
            AI is generating multiple options
          </p>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Empty state component when no logo is generated
 * @returns JSX element showing placeholder message
 */
function EmptyState() {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="flex flex-1 items-center justify-center"
      initial={{ opacity: 0 }}
    >
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/30">
          <IconColorFilter className="h-5 w-5 text-muted-foreground" />
        </div>
        <div>
          <h4 className="font-medium text-sm">Ready to Create</h4>
          <p className="mt-1 text-muted-foreground text-xs">
            Describe your perfect logo in the chat below to get started
          </p>
        </div>
      </div>
    </motion.div>
  );
}
