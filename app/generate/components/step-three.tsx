/**
 * Step Three: AI Settings Component
 * Allows user to configure AI model and output settings
 */

import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MODEL_OPTIONS, SIZE_OPTIONS } from '../constants';
import type {
  ModelType,
  QualityType,
  SizeType,
  StepThreeProps,
} from '../types';

/**
 * Third step of logo generation wizard - AI Settings
 * @param selectedModel - Currently selected AI model
 * @param setSelectedModel - Function to update selected model
 * @param selectedSize - Currently selected image size
 * @param setSelectedSize - Function to update selected size
 * @param selectedQuality - Currently selected quality setting
 * @param setSelectedQuality - Function to update quality setting
 * @param onNext - Callback to proceed to next step
 * @param onPrev - Callback to go back to previous step
 * @returns JSX element for AI model and output configuration
 */
export function StepThree({
  selectedModel,
  setSelectedModel,
  selectedSize,
  setSelectedSize,
  selectedQuality,
  setSelectedQuality,
  onNext,
  onPrev,
}: StepThreeProps) {
  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className="space-y-4"
      exit={{ opacity: 0, x: -20 }}
      initial={{ opacity: 0, x: 20 }}
    >
      <div>
        <h2 className="mb-1 font-semibold text-lg">AI Settings</h2>
        <p className="text-muted-foreground text-xs">
          Configure AI model and output
        </p>
      </div>

      <Card className="border border-border/50 shadow-sm">
        <CardContent className="space-y-4 p-4">
          <div className="space-y-2">
            <span className="font-medium text-xs">AI Model</span>
            <div className="space-y-2">
              {MODEL_OPTIONS.map((model) => (
                <motion.button
                  className={`w-full rounded-button border p-3 text-left transition-all ${
                    selectedModel === model.id
                      ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                      : 'border-border hover:border-primary/50 hover:bg-muted/50'
                  }`}
                  key={model.id}
                  onClick={() => setSelectedModel(model.id as ModelType)}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="mb-1 flex items-center gap-2">
                        <h3 className="font-medium text-sm">{model.name}</h3>
                        <Badge className="text-[10px]" variant="secondary">
                          {model.badge}
                        </Badge>
                      </div>
                      <p className="text-muted-foreground text-xs">
                        {model.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-xs">{model.speed}</div>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <span className="font-medium text-xs">Image Size</span>
              <Select
                onValueChange={(value: SizeType) => setSelectedSize(value)}
                value={selectedSize}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SIZE_OPTIONS.map((size) => (
                    <SelectItem key={size.id} value={size.id}>
                      {size.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <span className="font-medium text-xs">Quality</span>
              <Select
                onValueChange={(value: QualityType) =>
                  setSelectedQuality(value)
                }
                value={selectedQuality}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="hd">HD</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button className="h-8 px-4 text-xs" onClick={onPrev} variant="outline">
          <ChevronLeft className="mr-1 h-3 w-3" />
          Previous
        </Button>
        <Button className="h-8 px-4 text-xs" onClick={onNext}>
          Ready to Generate
          <ChevronRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </motion.div>
  );
}
