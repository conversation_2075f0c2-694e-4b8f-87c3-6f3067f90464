/**
 * Revolutionary Chat Interface for Logo Generation
 * AI-powered conversational design creation
 */

'use client';

import {
  IconHash,
  IconMicrophone,
  IconSend,
  IconSparkles,
} from '@tabler/icons-react';
import { AnimatePresence, motion } from 'framer-motion';
import { useRef, useState } from 'react';

// Top-level regex constants for performance
const TAG_END_REGEX = /\[([^\]]+)\]$/;
const TAG_START_REGEX = /^\[([^\]]+)\]/;
const TAG_SPLIT_REGEX = /(\[[^\]]+\])/;
const TAG_MATCH_REGEX = /\[([^\]]+)\]/;

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  isGenerating?: boolean;
}

interface ChatInterfaceProps {
  onGenerate: (prompt: string) => void;
  isGenerating: boolean;
  messages?: ChatMessage[]; // Optional since not used in instruction-only mode
  credits?: number | null;
}

interface PromptTag {
  id: string;
  label: string;
  value: string;
  category: 'style' | 'mood' | 'industry' | 'element' | 'color' | 'format';
  color: string;
}

/**
 * Revolutionary chat-first logo generation interface
 * @param onGenerate - Function to trigger logo generation
 * @param isGenerating - Whether AI is currently generating
 * @param messages - Chat conversation history
 * @returns JSX element for the chat interface
 */
export function ChatInterface({
  onGenerate,
  isGenerating,
  credits: _credits,
}: ChatInterfaceProps) {
  const [prompt, setPrompt] = useState('');
  const [_showSuggestions, _setShowSuggestions] = useState(true);
  const [showTagDropdown, setShowTagDropdown] = useState(false);
  const [selectedTags, setSelectedTags] = useState<PromptTag[]>([]);
  const [_cursorPosition, setCursorPosition] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Track text segments and tags inline
  const [_textSegments, _setTextSegments] = useState<
    Array<{
      type: 'text' | 'tag';
      content: string;
      tagId?: string;
      tagData?: PromptTag;
    }>
  >([{ type: 'text', content: '' }]);

  const handleSubmit = () => {
    const finalPrompt = buildPromptWithTags();
    if (finalPrompt && !isGenerating) {
      onGenerate(finalPrompt);
      setPrompt('');
      setSelectedTags([]);
      setShowTagDropdown(false);
    }
  };

  const _handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const _suggestions = [
    'Create a minimalist tech logo for my SaaS startup',
    "Design a modern logo for a coffee shop called 'Bean There'",
    'Make a professional logo for a law firm',
    "Create a playful logo for a kids' toy brand",
  ];

  const promptTags: PromptTag[] = [
    // Style tags
    {
      id: 'minimal',
      label: 'Minimal',
      value: 'minimal and clean',
      category: 'style',
      color: 'bg-blue-500',
    },
    {
      id: 'modern',
      label: 'Modern',
      value: 'modern and contemporary',
      category: 'style',
      color: 'bg-purple-500',
    },
    {
      id: 'vintage',
      label: 'Vintage',
      value: 'vintage and retro',
      category: 'style',
      color: 'bg-amber-500',
    },
    {
      id: 'elegant',
      label: 'Elegant',
      value: 'elegant and sophisticated',
      category: 'style',
      color: 'bg-rose-500',
    },

    // Mood tags
    {
      id: 'professional',
      label: 'Professional',
      value: 'professional and trustworthy',
      category: 'mood',
      color: 'bg-slate-500',
    },
    {
      id: 'playful',
      label: 'Playful',
      value: 'playful and fun',
      category: 'mood',
      color: 'bg-orange-500',
    },
    {
      id: 'bold',
      label: 'Bold',
      value: 'bold and impactful',
      category: 'mood',
      color: 'bg-red-500',
    },
    {
      id: 'calming',
      label: 'Calming',
      value: 'calming and peaceful',
      category: 'mood',
      color: 'bg-green-500',
    },

    // Industry tags
    {
      id: 'tech',
      label: 'Tech',
      value: 'technology company',
      category: 'industry',
      color: 'bg-cyan-500',
    },
    {
      id: 'finance',
      label: 'Finance',
      value: 'financial services',
      category: 'industry',
      color: 'bg-emerald-500',
    },
    {
      id: 'healthcare',
      label: 'Healthcare',
      value: 'healthcare provider',
      category: 'industry',
      color: 'bg-teal-500',
    },
    {
      id: 'food',
      label: 'Food & Drink',
      value: 'restaurant or food brand',
      category: 'industry',
      color: 'bg-lime-500',
    },

    // Element tags
    {
      id: 'geometric',
      label: 'Geometric',
      value: 'with geometric shapes',
      category: 'element',
      color: 'bg-indigo-500',
    },
    {
      id: 'text-only',
      label: 'Text Only',
      value: 'text-based wordmark',
      category: 'element',
      color: 'bg-gray-500',
    },
    {
      id: 'icon',
      label: 'With Icon',
      value: 'with a distinctive icon',
      category: 'element',
      color: 'bg-violet-500',
    },
    {
      id: 'monogram',
      label: 'Monogram',
      value: 'monogram style',
      category: 'element',
      color: 'bg-pink-500',
    },
  ];

  const addTag = (tag: PromptTag) => {
    if (!selectedTags.find((t) => t.id === tag.id)) {
      setSelectedTags((prev) => [...prev, tag]);

      // Insert tag at current cursor position in the text
      const currentText = textareaRef.current?.value || '';
      const cursorPos =
        textareaRef.current?.selectionStart || currentText.length;

      const beforeCursor = currentText.slice(0, cursorPos);
      const afterCursor = currentText.slice(cursorPos);

      // Add space before tag if needed
      const needsSpaceBefore = beforeCursor && !beforeCursor.endsWith(' ');
      const needsSpaceAfter = afterCursor && !afterCursor.startsWith(' ');

      const newText =
        beforeCursor +
        (needsSpaceBefore ? ' ' : '') +
        `[${tag.label}]` +
        (needsSpaceAfter ? ' ' : '') +
        afterCursor;

      setPrompt(newText);

      // Set cursor position after the tag
      setTimeout(() => {
        if (textareaRef.current) {
          const newCursorPos =
            cursorPos +
            (needsSpaceBefore ? 1 : 0) +
            `[${tag.label}]`.length +
            (needsSpaceAfter ? 1 : 0);
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          textareaRef.current.focus();
        }
      }, 0);
    }
  };

  const _removeTag = (tagId: string) => {
    const tag = selectedTags.find((t) => t.id === tagId);
    if (tag) {
      setSelectedTags((prev) => prev.filter((t) => t.id !== tagId));
      // Remove tag from text
      const tagPattern = `[${tag.label}]`;
      setPrompt((prevPrompt) =>
        prevPrompt.replace(tagPattern, '').replace(/\s+/g, ' ').trim()
      );
    }
  };

  const buildPromptWithTags = () => {
    // Replace tag markers with actual tag values
    let finalPrompt = prompt;
    for (const tag of selectedTags) {
      finalPrompt = finalPrompt.replace(`[${tag.label}]`, tag.value);
    }
    return finalPrompt.trim();
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleBackspaceDeletion = (
    textarea: HTMLTextAreaElement,
    text: string,
    cursorPos: number
  ) => {
    const beforeCursor = text.slice(0, cursorPos);
    const tagEndMatch = beforeCursor.match(TAG_END_REGEX);

    if (tagEndMatch) {
      const tagLabel = tagEndMatch[1];
      const tag = promptTags.find((t) => t.label === tagLabel);
      if (tag) {
        setSelectedTags((prev) => prev.filter((t) => t.id !== tag.id));
        const newText =
          text.slice(0, cursorPos - tagEndMatch[0].length) +
          text.slice(cursorPos);
        setPrompt(newText);
        setTimeout(() => {
          textarea.setSelectionRange(
            cursorPos - tagEndMatch[0].length,
            cursorPos - tagEndMatch[0].length
          );
        }, 0);
        return true;
      }
    }
    return false;
  };

  const handleDeleteKey = (
    textarea: HTMLTextAreaElement,
    text: string,
    cursorPos: number
  ) => {
    const afterCursor = text.slice(cursorPos);
    const tagStartMatch = afterCursor.match(TAG_START_REGEX);

    if (tagStartMatch) {
      const tagLabel = tagStartMatch[1];
      const tag = promptTags.find((t) => t.label === tagLabel);
      if (tag) {
        setSelectedTags((prev) => prev.filter((t) => t.id !== tag.id));
        const newText =
          text.slice(0, cursorPos) +
          text.slice(cursorPos + tagStartMatch[0].length);
        setPrompt(newText);
        setTimeout(() => {
          textarea.setSelectionRange(cursorPos, cursorPos);
        }, 0);
        return true;
      }
    }
    return false;
  };

  const processKeyDeletion = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
    textarea: HTMLTextAreaElement
  ) => {
    const cursorPos = textarea.selectionStart;
    const text = textarea.value;

    if (e.key === 'Backspace' && cursorPos > 0) {
      return handleBackspaceDeletion(textarea, text, cursorPos);
    }

    if (e.key === 'Delete' && cursorPos < text.length) {
      return handleDeleteKey(textarea, text, cursorPos);
    }

    return false;
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key !== 'Backspace' && e.key !== 'Delete') {
      return;
    }

    const textarea = textareaRef.current;
    if (!textarea) {
      return;
    }

    if (processKeyDeletion(e, textarea)) {
      e.preventDefault();
    }
  };

  return (
    <div>
      <div className="mx-auto max-w-4xl">
        {/* Clean Tag Dropdown */}
        <AnimatePresence>
          {showTagDropdown && (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 rounded-2xl border bg-card/95 p-4 shadow-lg backdrop-blur-sm"
              exit={{ opacity: 0, y: -10 }}
              initial={{ opacity: 0, y: -10 }}
            >
              <div className="mb-3 text-center font-medium text-sm">
                Add Smart Tags
              </div>
              <div className="grid grid-cols-2 gap-2 sm:grid-cols-4">
                {promptTags.map((tag) => (
                  <button
                    className={`rounded-full px-3 py-2 text-xs transition-all ${
                      selectedTags.find((t) => t.id === tag.id)
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted hover:bg-muted/80'
                    }`}
                    key={tag.id}
                    onClick={() => {
                      addTag(tag);
                      setShowTagDropdown(false);
                    }}
                    type="button"
                  >
                    {tag.label}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Modern Pill-Shaped Input Container */}
        <div className="relative">
          <div className="flex items-end rounded-2xl border bg-background/95 p-2 shadow-sm backdrop-blur-sm transition-all focus-within:border-primary/50 focus-within:shadow-md">
            {/* Tag Display and Input Container */}
            <div className="flex-1 px-4">
              <div className="relative">
                {/* Tag Display Overlay */}
                <div className="pointer-events-none absolute inset-0 z-10 flex items-start">
                  <div className="flex flex-wrap items-start gap-1 whitespace-pre-wrap break-words text-sm">
                    {prompt.split(TAG_SPLIT_REGEX).map((part, index) => {
                      const partKey = `${index}-${part.slice(0, 20)}`;
                      const tagMatch = part.match(TAG_MATCH_REGEX);
                      if (tagMatch) {
                        const tagLabel = tagMatch[1];
                        const tag = promptTags.find(
                          (t) => t.label === tagLabel
                        );
                        if (tag && selectedTags.find((t) => t.id === tag.id)) {
                          return (
                            <span
                              className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 font-medium text-primary text-xs"
                              key={partKey}
                            >
                              {tag.label}
                            </span>
                          );
                        }
                      }
                      return (
                        <span
                          className={
                            tagMatch ? 'text-transparent' : 'text-foreground'
                          }
                          key={partKey}
                        >
                          {part}
                        </span>
                      );
                    })}
                  </div>
                </div>

                {/* Clean Text Input (multiline, wraps, scrolls) */}
                <textarea
                  className="max-h-40 min-h-10 w-full resize-none overflow-y-auto whitespace-pre-wrap break-words bg-transparent py-3 text-sm leading-5 placeholder:text-muted-foreground focus:outline-none"
                  disabled={isGenerating}
                  onChange={handleTextChange}
                  onKeyDown={handleKeyDown}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit();
                    }
                  }}
                  placeholder={
                    isGenerating
                      ? 'Creating your logo...'
                      : 'What do you want to create today?'
                  }
                  ref={textareaRef}
                  rows={1}
                  style={{
                    color: 'transparent',
                    caretColor: 'rgb(var(--foreground))',
                  }}
                  value={prompt}
                />
              </div>
            </div>

            {/* Action Icons */}
            <div className="flex items-center gap-1">
              {/* Tags Button */}
              <button
                className={`flex h-9 w-9 items-center justify-center rounded-full transition-all ${
                  showTagDropdown
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                } ${selectedTags.length > 0 ? 'relative' : ''}`}
                disabled={isGenerating}
                onClick={() => setShowTagDropdown(!showTagDropdown)}
                title="Add Tags"
                type="button"
              >
                <IconHash className="h-4 w-4" />
                {selectedTags.length > 0 && (
                  <span className="-top-1 -right-1 absolute flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                    {selectedTags.length}
                  </span>
                )}
              </button>

              {/* Microphone Button */}
              <button
                className="flex h-9 w-9 items-center justify-center rounded-full transition-all hover:bg-muted"
                disabled={isGenerating}
                title="Voice Input"
                type="button"
              >
                <IconMicrophone className="h-4 w-4" />
              </button>

              {/* Generate Button */}
              <button
                className="ml-1 flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground transition-all hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
                disabled={!buildPromptWithTags() || isGenerating}
                onClick={handleSubmit}
                title={isGenerating ? 'Creating...' : 'Generate'}
                type="button"
              >
                {isGenerating ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: 'linear',
                    }}
                  >
                    <IconSparkles className="h-4 w-4" />
                  </motion.div>
                ) : (
                  <IconSend className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
