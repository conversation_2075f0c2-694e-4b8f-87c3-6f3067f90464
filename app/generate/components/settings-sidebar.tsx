/**
 * Elegant Collapsible Settings Sidebar - Inspired by competitor design
 * Clean, minimalist interface with expandable sections
 */

'use client';

import { ChevronRightIcon } from '@radix-ui/react-icons';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useMemo, useState } from 'react';
import { BACKGROUND_OPTIONS, COLOR_OPTIONS, STYLE_OPTIONS } from '../constants';

interface SettingsSidebarProps {
  selectedStyle: string;
  setSelectedStyle: (style: string) => void;
  primaryColor: string;
  setPrimaryColor: (color: string) => void;
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
}

/**
 * Simplified settings sidebar without step wizard
 * @param props - All settings and setters
 * @returns JSX element for the settings sidebar content
 */

/**
 * Simplified Color Picker Component
 */
interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  presets: Array<{ id: string; name: string }>;
  label: string;
}

function ColorPicker({ value, onChange, presets, label }: ColorPickerProps) {
  const [showCustom, setShowCustom] = useState(false);

  return (
    <div>
      <div className="mb-3 font-medium text-muted-foreground text-xs uppercase tracking-wide">
        {label}
      </div>

      {/* Color Presets - Fixed cropping with proper padding */}
      <div className="flex flex-wrap gap-2 p-1">
        {presets.slice(0, 5).map((color) => (
          <button
            className={`relative h-8 w-8 rounded-full border-2 transition-all ${
              value === color.id
                ? 'scale-110 border-primary'
                : 'border-border/20 hover:scale-105 hover:border-border/40'
            }`}
            key={color.id}
            onClick={() => onChange(color.id)}
            style={{ backgroundColor: color.id }}
            title={color.name}
            type="button"
          >
            {/* Selected indicator */}
            {value === color.id && (
              <div className="absolute inset-0 rounded-full border-2 border-white shadow-sm" />
            )}
          </button>
        ))}

        {/* Custom Color Button */}
        <button
          className={`relative h-8 w-8 rounded-full border-2 border-dashed transition-all ${
            showCustom
              ? 'border-primary bg-primary/10'
              : 'border-border/40 hover:border-border/60'
          }`}
          onClick={() => setShowCustom(!showCustom)}
          title="Custom color"
          type="button"
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-1 w-3 bg-current opacity-60" />
            <div className="absolute h-3 w-1 bg-current opacity-60" />
          </div>
        </button>
      </div>

      {/* Custom Color Input */}
      <AnimatePresence>
        {showCustom && (
          <motion.div
            animate={{ height: 'auto', opacity: 1 }}
            className="overflow-hidden"
            exit={{ height: 0, opacity: 0 }}
            initial={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="mt-3 flex gap-2">
              <input
                className="h-8 w-16 rounded border-2 border-border/20"
                onChange={(e) => onChange(e.target.value)}
                type="color"
                value={value}
              />
              <input
                className="flex-1 rounded border-2 border-border/20 px-2 text-sm"
                onChange={(e) => onChange(e.target.value)}
                placeholder="#000000"
                type="text"
                value={value}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/**
 * Collapsible section component for clean organization
 */
interface CollapsibleSectionProps {
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  icon?: React.ReactNode;
  summary?: React.ReactNode;
}

function CollapsibleSection({
  title,
  isExpanded,
  onToggle,
  children,
  icon,
  summary,
}: CollapsibleSectionProps) {
  return (
    <div className="border-border/20 border-b last:border-b-0">
      <button
        className="flex w-full items-center justify-between px-4 py-4 text-left transition-colors hover:bg-muted/30"
        onClick={onToggle}
        type="button"
      >
        <div className="flex items-center gap-3">
          {icon && <div className="text-muted-foreground">{icon}</div>}
          <span className="font-semibold text-base">{title}</span>
          {summary ? (
            <span className="rounded-full bg-muted px-2 py-0.5 text-[10px] text-muted-foreground">
              {summary}
            </span>
          ) : null}
        </div>
        <motion.div
          animate={{ rotate: isExpanded ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRightIcon className="h-4 w-4 text-muted-foreground" />
        </motion.div>
      </button>
      <AnimatePresence initial={false}>
        {isExpanded && (
          <motion.div
            animate={{ height: 'auto', opacity: 1 }}
            className="overflow-hidden"
            exit={{ height: 0, opacity: 0 }}
            initial={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="px-4 pb-6">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export function SettingsSidebar({
  selectedStyle,
  setSelectedStyle,
  primaryColor,
  setPrimaryColor,
  backgroundColor,
  setBackgroundColor,
}: SettingsSidebarProps) {
  // Collapsible sections state
  const [expandedSections, setExpandedSections] = useState({
    style: true,
    colors: true,
  });

  // Derived section summaries (small badges on headers)
  // summaries reserved for future header badges
  useMemo(
    () => ({ primary: primaryColor, background: backgroundColor }),
    [primaryColor, backgroundColor]
  );

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Keyboard shortcuts: 1..N selects style cards in order
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const tag = (e.target as HTMLElement | null)?.tagName?.toLowerCase();
      if (
        tag === 'input' ||
        tag === 'textarea' ||
        (e.target as HTMLElement)?.isContentEditable
      ) {
        return;
      }
      const idx = Number.parseInt(e.key, 10);
      if (!Number.isNaN(idx) && idx >= 1 && idx <= STYLE_OPTIONS.length) {
        const item = STYLE_OPTIONS[idx - 1];
        if (item?.id) {
          setSelectedStyle(item.id);
        }
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [setSelectedStyle]);

  // Persist settings (local device) for a smoother UX
  useEffect(() => {
    try {
      const payload = {
        selectedStyle,
        primaryColor,
        backgroundColor,
      };
      localStorage.setItem('superlogo:settings:v1', JSON.stringify(payload));
    } catch {
      // ignore
    }
  }, [selectedStyle, primaryColor, backgroundColor]);

  useEffect(() => {
    const raw = localStorage.getItem('superlogo:settings:v1');
    if (!raw) {
      return;
    }
    try {
      const data = JSON.parse(raw) as Partial<SettingsSidebarProps>;
      const setters: [unknown, () => void][] = [
        [
          typeof data.selectedStyle === 'string',
          () => setSelectedStyle(data.selectedStyle as string),
        ],
        [
          typeof data.primaryColor === 'string',
          () => setPrimaryColor(data.primaryColor as string),
        ],
        [
          typeof data.backgroundColor === 'string',
          () => setBackgroundColor(data.backgroundColor as string),
        ],
      ];
      for (const [cond, fn] of setters) {
        if (cond) {
          fn();
        }
      }
    } catch {
      // ignore
    }
  }, [setSelectedStyle, setPrimaryColor, setBackgroundColor]);

  return (
    <div className="-mx-1 divide-y divide-border/20">
      {/* Logo Style Section */}
      <CollapsibleSection
        isExpanded={expandedSections.style}
        onToggle={() => toggleSection('style')}
        summary={STYLE_OPTIONS.find((s) => s.id === selectedStyle)?.name}
        title="Logo Style"
      >
        <div className="grid grid-cols-2 gap-3">
          {STYLE_OPTIONS.map((style) => (
            <button
              className={`relative rounded-lg border-2 p-3 text-left transition-all ${
                selectedStyle === style.id
                  ? 'border-primary bg-primary/5'
                  : 'border-border/20 hover:border-border/40'
              }`}
              key={style.id}
              onClick={() => setSelectedStyle(style.id)}
              type="button"
            >
              <div
                className={`mb-2 inline-flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-r ${style.gradient}`}
              >
                <style.icon className="h-3 w-3 text-white" />
              </div>
              <div className="font-medium text-sm">{style.name}</div>
              <div className="mt-1 text-muted-foreground text-xs leading-relaxed">
                {style.description}
              </div>
            </button>
          ))}
        </div>
      </CollapsibleSection>

      {/* Colors Section */}
      <CollapsibleSection
        isExpanded={expandedSections.colors}
        onToggle={() => toggleSection('colors')}
        summary={<span>Primary • {primaryColor}</span>}
        title="Colors"
      >
        <div className="space-y-5">
          {/* Primary Color */}
          <ColorPicker
            label="Primary Color"
            onChange={setPrimaryColor}
            presets={COLOR_OPTIONS}
            value={primaryColor}
          />

          {/* Background Color */}
          <ColorPicker
            label="Background Color"
            onChange={setBackgroundColor}
            presets={BACKGROUND_OPTIONS}
            value={backgroundColor}
          />
        </div>
      </CollapsibleSection>

      {/* Removed duplicate AI Settings section; top dropdowns are the single source of truth */}
    </div>
  );
}
