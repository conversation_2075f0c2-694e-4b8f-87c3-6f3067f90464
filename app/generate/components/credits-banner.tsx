/**
 * Enhanced credits banner component with progress visualization
 * Shows remaining credits with animated progress bar and status text
 */

import { motion } from 'framer-motion';

interface CreditsBannerProps {
  remaining: number | null;
}

/**
 * Enhanced credits banner component
 * @param remaining - Number of remaining credits (null if loading)
 * @returns JSX element displaying credits with progress bar
 */
export function CreditsBanner({ remaining }: CreditsBannerProps) {
  // For display purposes, show progress bar based on reasonable ranges
  const getProgressPercentage = (credits: number | null) => {
    if (!credits) {
      return 0;
    }
    // Show 100% if user has credits, visual feedback only
    return credits > 0 ? Math.min(credits, 100) : 0;
  };

  const percentage = getProgressPercentage(remaining);

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="mb-3 flex items-center justify-between">
        <span className="font-medium text-muted-foreground text-sm">
          Credits
        </span>
        <span className="font-semibold text-lg">{remaining ?? '—'}</span>
      </div>
      <div className="mb-2">
        <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
          <motion.div
            animate={{ width: `${percentage}%` }}
            className="h-full rounded-full bg-primary"
            initial={{ width: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        </div>
      </div>
      <p className="text-muted-foreground text-xs">
        {remaining && remaining > 0
          ? `${remaining} credits remaining`
          : 'No credits available'}
      </p>
    </div>
  );
}
