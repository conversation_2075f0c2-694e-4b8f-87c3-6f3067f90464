'use client';

import Image from 'next/image';

interface HistoryItem {
  id: number;
  createdAt: string;
  thumbnailUrl: string;
  prompt?: string | null;
}

interface HistorySidebarProps {
  /**
   * List of history entries for the current project.
   * Each entry represents a single generated logo; selecting it loads the
   * sibling pair (the two logos generated together) via the siblings API.
   */
  items: HistoryItem[];
  /** Currently selected history entry id. */
  selectedId?: number | null;
  /** Called when a history entry is selected. */
  onSelect: (id: number) => void;
}

/**
 * HistorySidebar
 *
 * Purpose: Right-side vertical bar showing the list of prior generations for a
 * project. Inspired by competitor's history rail UX.
 *
 * Why: Users should switch between historical generations without leaving the
 * editor. This removes the need for per-logo "Open in Generator" actions.
 *
 * @param items - Ordered list of history items (newest first recommended)
 * @param selectedId - Currently active generation id
 * @param onSelect - Callback invoked when a generation is picked
 * @returns JSX.Element
 * @example
 * <HistorySidebar items={history} selectedId={curId} onSelect={setId} />
 */
export function HistorySidebar({
  items,
  selectedId,
  onSelect,
}: HistorySidebarProps) {
  if (!Array.isArray(items) || items.length === 0) {
    return (
      <div className="flex h-full items-center justify-center p-4 text-center text-muted-foreground text-xs">
        No history yet
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-y-auto overflow-x-hidden p-3">
      <div className="grid gap-2">
        {items.map((item) => {
          const isActive = selectedId === item.id;
          return (
            <button
              aria-pressed={isActive}
              className={`flex w-full items-center gap-3 overflow-hidden rounded-xl border px-2 py-2 transition-colors hover:bg-accent ${
                isActive ? 'border-primary bg-primary/5' : 'border-border/60'
              } text-left`}
              key={item.id}
              onClick={() => onSelect(item.id)}
              type="button"
            >
              <div className="relative h-10 w-10 overflow-hidden rounded-lg bg-muted">
                {item.thumbnailUrl && (
                  <Image
                    alt={item.prompt ?? 'Logo thumbnail'}
                    className="object-cover"
                    fill
                    src={item.thumbnailUrl}
                  />
                )}
              </div>
              <div className="min-w-0 flex-1">
                <div className="truncate font-medium text-xs">
                  {new Date(item.createdAt).toLocaleString()}
                </div>
                {item.prompt && (
                  <div
                    className="truncate text-[11px] text-muted-foreground"
                    title={item.prompt ?? undefined}
                  >
                    {item.prompt}
                  </div>
                )}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
}

export type { HistoryItem, HistorySidebarProps };
