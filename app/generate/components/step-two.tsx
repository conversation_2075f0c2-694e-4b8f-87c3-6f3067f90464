/**
 * Step Two: Style & Colors Component
 * Allows user to select logo style and color preferences
 */

import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { BACKGROUND_OPTIONS, COLOR_OPTIONS, STYLE_OPTIONS } from '../constants';
import type { StepTwoProps } from '../types';

/**
 * Second step of logo generation wizard - Style & Colors
 * @param selectedStyle - Currently selected style ID
 * @param setSelectedStyle - Function to update selected style
 * @param primaryColor - Currently selected primary color
 * @param setPrimaryColor - Function to update primary color
 * @param backgroundColor - Currently selected background color
 * @param setBackgroundColor - Function to update background color
 * @param onNext - Callback to proceed to next step
 * @param onPrev - Callback to go back to previous step
 * @returns JSX element for style and color selection
 */
export function StepTwo({
  selectedStyle,
  setSelectedStyle,
  primaryColor,
  setPrimaryColor,
  backgroundColor,
  setBackgroundColor,
  onNext,
  onPrev,
}: StepTwoProps) {
  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className="space-y-4"
      exit={{ opacity: 0, x: -20 }}
      initial={{ opacity: 0, x: 20 }}
    >
      <div>
        <h2 className="mb-1 font-semibold text-lg">Style & Colors</h2>
        <p className="text-muted-foreground text-xs">
          Choose your style and colors
        </p>
      </div>

      <Card className="border border-border/50 shadow-sm">
        <CardContent className="space-y-4 p-4">
          <div className="space-y-2">
            <span className="font-medium text-xs">Logo Style</span>
            <div className="grid grid-cols-2 gap-3">
              {STYLE_OPTIONS.map((style) => (
                <motion.button
                  className={`relative rounded-button border p-4 text-left transition-all ${
                    selectedStyle === style.id
                      ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                      : 'border-border hover:border-primary/50 hover:bg-muted/50'
                  }`}
                  key={style.id}
                  onClick={() => setSelectedStyle(style.id)}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <div
                    className={`mb-3 inline-flex h-8 w-8 items-center justify-center rounded-button bg-gradient-to-r ${style.gradient}`}
                  >
                    <style.icon className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="mb-1 font-medium text-sm">{style.name}</h3>
                  <p className="text-muted-foreground text-xs leading-relaxed">
                    {style.description}
                  </p>
                </motion.button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <span className="font-medium text-xs">Primary Color</span>
              <Select onValueChange={setPrimaryColor} value={primaryColor}>
                <SelectTrigger className="h-8">
                  <SelectValue>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full border"
                        style={{ backgroundColor: primaryColor }}
                      />
                      <span className="text-xs">
                        {COLOR_OPTIONS.find((c) => c.id === primaryColor)
                          ?.name || 'Select Color'}
                      </span>
                    </div>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {COLOR_OPTIONS.map((color) => (
                    <SelectItem key={color.id} value={color.id}>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full border"
                          style={{ backgroundColor: color.id }}
                        />
                        <span className="text-xs">{color.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <span className="font-medium text-xs">Background</span>
              <Select
                onValueChange={setBackgroundColor}
                value={backgroundColor}
              >
                <SelectTrigger className="h-8">
                  <SelectValue>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full border"
                        style={{ backgroundColor }}
                      />
                      <span className="text-xs">
                        {BACKGROUND_OPTIONS.find(
                          (c) => c.id === backgroundColor
                        )?.name || 'Select Background'}
                      </span>
                    </div>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {BACKGROUND_OPTIONS.map((color) => (
                    <SelectItem key={color.id} value={color.id}>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full border"
                          style={{ backgroundColor: color.id }}
                        />
                        <span className="text-xs">{color.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button className="h-8 px-4 text-xs" onClick={onPrev} variant="outline">
          <ChevronLeft className="mr-1 h-3 w-3" />
          Previous
        </Button>
        <Button className="h-8 px-4 text-xs" onClick={onNext}>
          Next: AI Settings
          <ChevronRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </motion.div>
  );
}
