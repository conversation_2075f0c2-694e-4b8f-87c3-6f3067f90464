/**
 * Step Four: Generate Component
 * Final step to trigger logo generation
 */

import { IconSparkles } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { ChevronLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { StepFourProps } from '../types';

/**
 * Fourth step of logo generation wizard - Generate Logo
 * @param isFormValid - Whether the form data is valid for generation
 * @param loading - Whether generation is currently in progress
 * @param onGenerate - Callback to trigger logo generation
 * @param onPrev - Callback to go back to previous step
 * @returns JSX element for logo generation trigger
 */
export function StepFour({
  isFormValid,
  loading,
  onGenerate,
  onPrev,
}: StepFourProps) {
  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className="space-y-4"
      exit={{ opacity: 0, x: -20 }}
      initial={{ opacity: 0, x: 20 }}
    >
      <div>
        <h2 className="mb-1 font-semibold text-lg">Generate Logo</h2>
        <p className="text-muted-foreground text-xs">
          All set! Create your logo with AI
        </p>
      </div>

      <Card className="border border-border/50 shadow-sm">
        <CardContent className="space-y-4 p-4 text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-primary to-blue-600">
            <IconSparkles className="h-8 w-8 text-white" />
          </div>

          <div>
            <h3 className="mb-1 font-medium text-sm">Ready to Create</h3>
            <p className="text-muted-foreground text-xs">
              Generate your unique logo with AI
            </p>
          </div>

          <Button
            className="h-8 w-full text-xs"
            disabled={!isFormValid || loading}
            onClick={onGenerate}
          >
            {loading ? (
              <>
                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <IconSparkles className="mr-1 h-3 w-3" />
                Generate Logo
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <div className="flex justify-start">
        <Button className="h-8 px-4 text-xs" onClick={onPrev} variant="outline">
          <ChevronLeft className="mr-1 h-3 w-3" />
          Previous
        </Button>
      </div>
    </motion.div>
  );
}
