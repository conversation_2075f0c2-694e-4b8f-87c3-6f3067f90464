/**
 * Enhanced Logo Preview Component
 * Displays generated logo with download and regeneration options
 */

import { IconColorFilter, IconEdit } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { Download, RefreshCw } from 'lucide-react';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { LogoPreviewProps } from '../types';

/**
 * Enhanced logo preview component with actions
 * @param generatedLogo - URL of the generated logo image
 * @param backgroundColor - Background color for logo display
 * @param loading - Whether logo generation is in progress
 * @param onDownload - Callback to download the logo
 * @param onRegenerate - Callback to regenerate the logo
 * @returns JSX element displaying logo preview and action buttons
 */
export function LogoPreview({
  generatedLogo,
  backgroundColor,
  loading,
  onDownload,
  onRegenerate,
}: LogoPreviewProps) {
  return (
    <Card className="h-full border border-border/50 bg-card shadow-sm">
      <CardContent className="flex h-full flex-col p-6">
        {(() => {
          if (loading) {
            return <LoadingState />;
          }
          if (generatedLogo) {
            return (
              <GeneratedLogoState
                backgroundColor={backgroundColor}
                generatedLogo={generatedLogo}
                onDownload={onDownload}
                onRegenerate={onRegenerate}
              />
            );
          }
          return <EmptyState />;
        })()}
      </CardContent>
    </Card>
  );
}

/**
 * Loading state component for logo generation
 * @returns JSX element showing loading spinner and message
 */
function LoadingState() {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="flex flex-1 items-center justify-center"
      initial={{ opacity: 0 }}
    >
      <div className="space-y-3 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <RefreshCw className="h-5 w-5 animate-spin text-primary" />
        </div>
        <div>
          <h4 className="font-medium text-xs">Generating...</h4>
          <p className="text-[10px] text-muted-foreground">Please wait</p>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Generated logo state component
 * @param backgroundColor - Background color for logo display
 * @param generatedLogo - URL of the generated logo
 * @param onDownload - Download callback
 * @param onRegenerate - Regenerate callback
 * @returns JSX element showing logo and action buttons
 */
function GeneratedLogoState({
  backgroundColor,
  generatedLogo,
  onDownload,
  onRegenerate,
}: {
  backgroundColor: string;
  generatedLogo: string;
  onDownload: () => void;
  onRegenerate: () => void;
}) {
  return (
    <motion.div
      animate={{ opacity: 1, scale: 1 }}
      className="flex flex-1 flex-col"
      initial={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.4 }}
    >
      <div className="mb-4 flex flex-1 items-center justify-center">
        <div
          className="relative aspect-square w-full max-w-xs rounded-xl border border-border/20 shadow-sm"
          style={{ backgroundColor }}
        >
          {generatedLogo && generatedLogo.trim() !== '' && (
            <Image
              alt="Generated logo"
              className="rounded-xl object-contain p-6"
              fill
              src={generatedLogo}
            />
          )}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-center gap-2">
          <Button
            className="h-7 px-3 text-xs"
            onClick={onRegenerate}
            size="sm"
            variant="outline"
          >
            <RefreshCw className="mr-1 h-3 w-3" />
            Retry
          </Button>
          <Button className="h-7 px-3 text-xs" onClick={onDownload} size="sm">
            <Download className="mr-1 h-3 w-3" />
            Download
          </Button>
        </div>

        <div className="flex justify-center">
          <Button
            className="h-6 gap-1 px-2 text-xs"
            disabled
            size="sm"
            variant="ghost"
          >
            <IconEdit className="h-3 w-3" />
            Edit in Canvas
            <Badge className="px-1 py-0 text-[9px]" variant="secondary">
              Soon
            </Badge>
          </Button>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Empty state component when no logo is generated
 * @returns JSX element showing placeholder message
 */
function EmptyState() {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="flex flex-1 items-center justify-center"
      initial={{ opacity: 0 }}
    >
      <div className="max-w-xs space-y-3 text-center">
        <div className="mx-auto flex h-14 w-14 items-center justify-center rounded-full bg-muted/50">
          <IconColorFilter className="h-6 w-6 text-muted-foreground" />
        </div>
        <div>
          <h4 className="mb-1 font-medium text-sm">Ready to Generate</h4>
          <p className="text-muted-foreground text-xs">
            Complete the steps to create your logo
          </p>
        </div>
      </div>
    </motion.div>
  );
}
