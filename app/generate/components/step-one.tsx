/**
 * Step One: Brand Details Component
 * Collects company name and additional brand information
 */

import { motion } from 'framer-motion';
import { Building2, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import type { StepOneProps } from '../types';

/**
 * First step of logo generation wizard - Brand Details
 * @param companyName - Current company name value
 * @param setCompanyName - Function to update company name
 * @param additionalInfo - Current additional info value
 * @param setAdditionalInfo - Function to update additional info
 * @param onNext - Callback to proceed to next step
 * @returns JSX element for brand details form
 */
export function StepOne({
  companyName,
  setCompanyName,
  additionalInfo,
  setAdditionalInfo,
  onNext,
}: StepOneProps) {
  const isValid = companyName.trim().length > 0;

  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className="space-y-4"
      exit={{ opacity: 0, x: -20 }}
      initial={{ opacity: 0, x: 20 }}
    >
      <div>
        <h2 className="mb-1 font-semibold text-lg">Brand Details</h2>
        <p className="text-muted-foreground text-xs">
          Tell us about your brand
        </p>
      </div>

      <Card className="border border-border/50 shadow-sm">
        <CardContent className="space-y-4 p-4">
          <div className="space-y-2">
            <label className="font-medium text-xs" htmlFor="brandName">
              Brand Name *
            </label>
            <div className="relative">
              <Building2 className="-translate-y-1/2 pointer-events-none absolute top-1/2 left-2 h-4 w-4 opacity-60" />
              <Input
                className="pl-8"
                id="brandName"
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Enter your brand name"
                value={companyName}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="font-medium text-xs" htmlFor="additionalDetails">
              Additional Details
            </label>
            <Textarea
              className="min-h-20 resize-none text-sm"
              id="additionalDetails"
              onChange={(e) => setAdditionalInfo(e.target.value)}
              placeholder="Describe your brand personality, target audience, or any specific preferences..."
              value={additionalInfo}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button
          className="h-8 px-4 text-xs"
          disabled={!isValid}
          onClick={onNext}
        >
          Next: Style & Colors
          <ChevronRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </motion.div>
  );
}
