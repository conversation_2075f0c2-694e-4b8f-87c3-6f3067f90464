/**
 * Logo Generation Layout Component
 * Main layout wrapper with sidebar and content areas
 */

import { SignedIn, UserButton } from '@clerk/nextjs';
import { IconEdit, IconFolder, IconWand } from '@tabler/icons-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ToggleTheme } from '@/components/theme-toggler';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CreditsPill } from '@/components/ui/credits-pill';

// import { CreditsBanner } from './credits-banner';

interface GenerationLayoutProps {
  credits: number | null;
  monthlyLimit?: number | null;
  plan?: string | null;
  loading?: boolean;
  generatedLogos: string[];
  children: React.ReactNode;
  previewPanel: React.ReactNode;
  chatPanel: React.ReactNode;
  projectName?: string | null;
  /** Optional right-side history sidebar */
  rightSidebar?: React.ReactNode;
}

/**
 * Revolutionary chat-first logo generation interface
 * @param credits - Remaining user credits
 * @param generatedLogos - Array of generated logo URLs
 * @param children - Style and settings sidebar content
 * @param previewPanel - Main preview area content
 * @param chatPanel - Bottom chat interface
 * @returns JSX element for the complete generation layout
 */
export function GenerationLayout({
  credits: _credits,
  monthlyLimit: _monthlyLimit,
  plan: _plan,
  loading: _loading,
  generatedLogos,
  children,
  previewPanel,
  chatPanel,
  projectName,
  rightSidebar,
}: GenerationLayoutProps) {
  const router = useRouter();

  return (
    <div className="relative flex h-screen bg-background">
      {/* Settings Sidebar (Left) - Floating card */}
      <aside className="mx-4 my-4 flex h-[calc(100vh-2rem)] w-96 shrink-0 flex-col overflow-hidden rounded-2xl border bg-background/95 shadow-xl backdrop-blur supports-[backdrop-filter]:bg-background/75">
        {/* Header */}
        <div className="flex h-14 items-center justify-between border-border/10 border-b px-4">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
              <IconWand className="h-4 w-4 text-primary-foreground" />
            </div>
            <h2 className="font-semibold text-sm">Style & Settings</h2>
          </div>
          <div className="flex items-center gap-2">
            <ToggleTheme />
          </div>
        </div>

        {/* Settings Content - Clean scroll */}
        <div className="flex-1 overflow-y-auto p-4">{children}</div>

        {/* Quick Actions Footer */}
        <div className="border-border/10 border-t p-4">
          <div className="flex justify-center">
            <Button
              className="justify-start gap-2"
              onClick={() => router.push('/projects')}
              size="sm"
              variant="ghost"
            >
              <IconFolder className="h-4 w-4" />
              Projects
            </Button>
          </div>
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col">
        {/* Top Navigation Bar */}
        <div className="flex h-14 items-center justify-between border-border/10 border-b px-5">
          <div className="flex items-center gap-3">
            <h1 className="font-semibold">
              {projectName ? `${projectName} - Logo Preview` : 'Logo Preview'}
            </h1>
            {projectName && (
              <Badge className="text-xs" variant="secondary">
                Project
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-3">
            <div className="hidden md:flex">
              <CreditsPill />
            </div>
            <Link href="/pricing">
              <Button size="sm" variant="default">
                Upgrade
              </Button>
            </Link>
            <Button
              disabled={generatedLogos.length === 0}
              size="sm"
              variant="secondary"
            >
              <IconEdit className="mr-2 h-4 w-4" />
              Edit Canvas
              <Badge className="ml-2 h-4 px-1 text-[9px]" variant="secondary">
                Soon
              </Badge>
            </Button>
            <Link href="/account/billing">
              <Button size="sm" variant="ghost">
                Manage plan
              </Button>
            </Link>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </div>
        </div>

        {/* Preview Content with optional right sidebar */}
        <div className="flex flex-1 overflow-hidden">
          {/* Main Preview (make relative so chat can be centered within this column) */}
          <div className="relative flex-1 p-6 pb-28">
            {previewPanel}
            {/* Floating Chat anchored to preview column width */}
            <div className="pointer-events-none absolute inset-x-0 bottom-0 z-50 flex justify-center p-6">
              <div className="pointer-events-auto w-full">{chatPanel}</div>
            </div>
          </div>

          {/* Right History Sidebar (optional) */}
          {rightSidebar && (
            <aside className="mx-4 my-4 hidden h-[calc(100vh-6.5rem)] w-72 shrink-0 flex-col overflow-hidden rounded-2xl border bg-background/95 shadow-xl backdrop-blur supports-[backdrop-filter]:bg-background/75 lg:flex">
              <div className="flex h-12 items-center justify-between border-border/10 border-b px-3">
                <h3 className="font-semibold text-sm">History</h3>
              </div>
              <div className="flex-1 overflow-y-auto overflow-x-hidden">
                {rightSidebar}
              </div>
            </aside>
          )}
        </div>
      </div>

      {/* Chat is now centered within the preview column above; global bar removed */}
    </div>
  );
}
