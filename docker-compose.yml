version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
    container_name: superlogo
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      POSTGRES_URL: **************************************/superlogo
      NEBIUS_API_KEY: ${NEBIUS_API_KEY}
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      UPSTASH_REDIS_REST_URL: ${UPSTASH_REDIS_REST_URL}
      UPSTASH_REDIS_REST_TOKEN: ${UPSTASH_REDIS_REST_TOKEN}
      HELICONE_API_KEY: ${HELICONE_API_KEY}
      NEXT_PUBLIC_DEVELOPMENT_URL: ${NEXT_PUBLIC_DEVELOPMENT_URL}
    depends_on:
      - postgres
    networks:
      - backend

  postgres:
    image: postgres:13
    container_name: postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: superlogo
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend

volumes:
  postgres_data:

networks:
  backend:
    driver: bridge

