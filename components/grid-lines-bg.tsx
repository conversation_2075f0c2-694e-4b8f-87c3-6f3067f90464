import type React from 'react';

export default function GridLinesBg({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative flex h-[50rem] w-full items-center justify-center bg-grid-black/[0.2] bg-white dark:bg-black dark:bg-grid-white/[0.2]">
      {/* Radial gradient for the container to give a faded look */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black" />
      <p className="relative z-20 bg-gradient-to-b from-neutral-200 to-neutral-500 bg-clip-text py-8 font-bold text-4xl text-transparent sm:text-7xl">
        {children}
      </p>
    </div>
  );
}
