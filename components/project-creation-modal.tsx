'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

interface ProjectCreationModalProps {
  children: React.ReactNode;
  onProjectCreated?: (projectName: string) => void;
}

/**
 * Project creation modal component
 * Allows users to create a new project with a simple name input
 */
export function ProjectCreationModal({
  children,
  onProjectCreated,
}: ProjectCreationModalProps) {
  const [open, setOpen] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectName.trim()) {
      toast({
        title: 'Error',
        description: 'Project name is required',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/projects/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: projectName.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: 'Project created successfully',
      });

      // Reset form and close modal
      setProjectName('');
      setOpen(false);

      // Callback with project name for navigation
      onProjectCreated?.(result.project.name);
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create project',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setProjectName('');
    setOpen(false);
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogTitle>Create New Project</DialogTitle>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Input
                autoFocus
                disabled={isLoading}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="Project name..."
                value={projectName}
              />
            </div>
            <div className="flex gap-3 pt-2">
              <Button
                disabled={isLoading}
                onClick={handleCancel}
                size="default"
                type="button"
                variant="secondary"
              >
                Cancel
              </Button>
              <Button
                disabled={isLoading || !projectName.trim()}
                size="default"
                type="submit"
              >
                {isLoading ? 'Creating...' : 'Create Project'}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
