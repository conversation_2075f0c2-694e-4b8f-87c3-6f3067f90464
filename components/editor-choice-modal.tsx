'use client';

import { <PERSON><PERSON>, Brush } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';

interface EditorChoiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectName: string;
}

/**
 * Editor choice modal component
 * Allows users to choose between AI Editor and Canvas Editor after project creation
 */
export function EditorChoiceModal({
  open,
  onOpenChange,
  projectName,
}: EditorChoiceModalProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);
  const [selectedEditor, setSelectedEditor] = useState<'ai' | 'canvas'>('ai');

  const handleAIEditor = () => {
    setIsNavigating(true);
    // Navigate to generate page with project context as URL parameter
    router.push(`/generate?project=${encodeURIComponent(projectName)}`);
  };

  const handleContinue = () => {
    if (selectedEditor === 'ai') {
      handleAIEditor();
    } else {
      // For now, redirect to AI editor even if canvas is selected
      handleAIEditor();
    }
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent>
        <DialogTitle>Choose Your Editor</DialogTitle>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            <button
              className={`flex flex-col items-center gap-3 rounded-lg border-2 p-4 transition-all hover:scale-105 ${
                selectedEditor === 'ai'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
              }`}
              onClick={() => setSelectedEditor('ai')}
              type="button"
            >
              <Bot className="h-8 w-8 text-blue-600" />
              <div className="text-center">
                <div className="font-semibold text-sm">AI Editor</div>
                <Badge size="1" variant="secondary">
                  Recommended
                </Badge>
              </div>
            </button>

            <button
              className="flex flex-col items-center gap-3 rounded-lg border-2 border-gray-200 bg-gray-50 p-4 opacity-60"
              disabled
              type="button"
            >
              <Brush className="h-8 w-8 text-gray-400" />
              <div className="text-center">
                <div className="font-semibold text-sm">Canvas Editor</div>
                <Badge size="1" variant="outline">
                  Coming Soon
                </Badge>
              </div>
            </button>
          </div>

          <div className="flex justify-center">
            <Button
              disabled={isNavigating}
              onClick={handleContinue}
              size="default"
            >
              {isNavigating ? 'Loading...' : 'Continue'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
