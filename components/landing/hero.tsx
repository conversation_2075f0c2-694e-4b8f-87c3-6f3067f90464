'use client';

import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { IconPointerFilled } from '@tabler/icons-react';
import Link from 'next/link';
import { domain } from '@/lib/domain';
import FloatingIcons from '../floating-icons';
import Gradient from '../gradient';
import { Button } from '../ui/button';

export default function Hero() {
  return (
    <div className="relative overflow-hidden">
      <section className="relative mx-auto flex h-full max-w-6xl flex-col items-center justify-center px-4 pt-24 sm:pt-36">
        <Gradient />
        <FloatingIcons />

        <div className="text-center font-medium text-4xl sm:text-5xl md:text-5xl lg:text-7xl">
          Create stunning logos with <br />{' '}
          <span className="bg-gradient-to-tr from-white via-primary to-white bg-clip-text font-semibold text-transparent">
            AI powered
          </span>{' '}
          design
        </div>

        <div className="mt-8 w-full text-center font-bold text-base text-neutral-500 md:text-lg lg:w-[50%]">
          Generate unique,{' '}
          <span className="font-extrabold text-neutral-900 dark:font-bold dark:text-neutral-300">
            professional
          </span>{' '}
          logos in seconds. <br className="hidden md:block" />
          <span className="font-extrabold text-neutral-900 dark:font-bold dark:text-neutral-300">
            Perfect for
          </span>{' '}
          businesses, startups, and personal brands.
        </div>

        <div className="mt-10 flex w-full flex-col items-center gap-4 sm:flex-row md:w-auto">
          <SignedIn>
            <Link className="w-full md:w-auto" href="/generate">
              <Button className="h-8 w-full px-6 py-5 transition-all hover:scale-105 hover:opacity-90">
                Try for free! <IconPointerFilled className="h-4 w-4" />
              </Button>
            </Link>
          </SignedIn>
          <SignedOut>
            <SignInButton
              forceRedirectUrl={`${domain}/pricing`}
              signUpForceRedirectUrl={`${domain}/pricing`}
            >
              <Button className="h-8 w-full px-6 py-5 transition-all hover:scale-105 hover:opacity-90">
                Get started <IconPointerFilled className="h-4 w-4" />
              </Button>
            </SignInButton>
          </SignedOut>
        </div>
      </section>
    </div>
  );
}
