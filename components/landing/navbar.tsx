'use client';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';
import { LoaderIcon } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { domain } from '@/lib/domain';
import { ToggleTheme } from '../theme-toggler';
import { Button } from '../ui/button';

export default function Navbar() {
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);
  return (
    <header className="fixed top-0 right-0 left-0 z-50 mx-auto max-w-6xl px-4">
      <nav className="mt-4 flex items-center justify-between rounded-xl border border-accent/50 px-5 py-2 text-sm backdrop-blur-md">
        <Link className="flex-1 font-semibold" href="/">
          Superlogo
        </Link>
        <div className="hidden items-center space-x-8 font-semibold md:flex">
          <Link href="/#features">Features</Link>
          <Link href="/#faq">FAQs</Link>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <ToggleTheme />
          {!isMounted && (
            <Button>
              <LoaderIcon className="animate-spin" />
            </Button>
          )}
          <SignedOut>
            <SignInButton
              forceRedirectUrl={`${domain}/generate`}
              mode="modal"
              signUpForceRedirectUrl={`${domain}/generate`}
            >
              <Button className="text-sm">Sign In</Button>
            </SignInButton>
          </SignedOut>
          <SignedIn>
            <UserButton />
            {/* <Link href="/generate">
              <Button className="text-white">
                Get Started
              </Button> 
            </Link> */}
          </SignedIn>
        </div>
      </nav>
    </header>
  );
}
