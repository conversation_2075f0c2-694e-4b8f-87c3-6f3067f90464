import { IconCrown } from '@tabler/icons-react';
import { features } from '@/constants/data';
import { cn } from '@/lib/utils';
import { Badge } from '../ui/badge';

export default function Features() {
  return (
    <div className="mt-20 bg-background py-10" id="features">
      <div className="flex flex-col">
        <Badge className="mb-4 flex w-fit items-center gap-2 px-4 py-2">
          <IconCrown className="size-4 text-white" />
          Features
        </Badge>
        <div className="font-medium text-4xl md:text-6xl">
          Key
          <span className="mx-2 bg-gradient-to-tr from-white via-primary to-white bg-clip-text text-transparent">
            Benifits
          </span>
          of using
          <br />{' '}
          <span className="text-3xl text-muted-foreground/40 md:text-5xl">
            Our Logo generator
          </span>
        </div>
      </div>

      <section className="mt-10 grid grid-cols-1 gap-2 md:grid-cols-3">
        {features.map((feature, index) => (
          <div
            className={cn(
              'min-h-[20rem] rounded-3xl border border-border/40 p-2 hover:shadow-md',
              index === 0 ? 'md:col-span-1 md:row-span-4' : '',
              index === 3 ? 'h-[20rem] md:col-span-2 md:row-span-3' : ''
            )}
            key={feature.title}
          >
            <div
              className={cn(
                'group relative flex h-full max-h-full flex-col overflow-hidden rounded-2xl border border-border/60 bg-background p-4 transition-all hover:border-accent'
              )}
            >
              <div className="absolute right-[-20rem] bottom-[-5rem] z-[1] size-[20rem] translate-x-[-50%] rotate-[40deg] rounded-xl bg-gradient-to-t from-primary to-purple-800/20 opacity-0 blur-[5em] transition-all duration-700 ease-out group-hover:opacity-30 dark:group-hover:opacity-80" />
              <div
                className={cn(
                  'absolute h-full w-full',
                  index === 0 ? 'md:-top-[10%]' : '',
                  index === 3
                    ? '-top-[20%] -left-[35%] md:-top-[20%] md:-left-[43%]'
                    : '-top-[20%] -left-[35%] md:-left-[35%]'
                )}
              >
                <div className="relative z-[10] flex flex-col items-center justify-center">
                  <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 h-36 w-36 rounded-full border-primary/40 bg-primary/10" />
                  <div className="top-0 left-0 h-56 w-56 rounded-full border-primary bg-primary/5" />
                </div>
              </div>
              <div className="z-20 mb-4 flex-1 text-4xl">
                <div className="flex w-fit items-center justify-center rounded-full bg-primary p-4">
                  {<feature.icon className="size-10 text-white" />}
                </div>
              </div>
              <div>
                <h3 className="mb-2 font-bold text-xl">{feature.title}</h3>
                <p className="font-semibold text-gray-500 text-sm">
                  {feature.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </section>
    </div>
  );
}
