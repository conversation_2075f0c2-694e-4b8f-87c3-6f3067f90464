import { IconBrandLine } from '@tabler/icons-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { faqItems } from '@/constants/data';
import { Badge } from '../ui/badge';

export default function Faq() {
  return (
    <div className="mt-20 bg-background py-10" id="faq">
      <div className="flex flex-col">
        <Badge className="mb-4 flex w-fit items-center gap-2 px-4 py-2">
          <IconBrandLine className="size-4 text-white" />
          FAQs
        </Badge>
        <div className="font-medium text-4xl md:text-6xl">
          Recently raised
          <span className="mx-2 bg-gradient-to-tr from-white via-primary to-white bg-clip-text text-transparent">
            queries
          </span>
          <br />{' '}
          <span className="text-3xl text-muted-foreground/40 md:text-5xl">
            about Superlogo
          </span>
        </div>
      </div>
      <Accordion className="mt-10 w-full" collapsible type="single">
        {faqItems.map((item, index) => (
          <AccordionItem
            className="mt-2 rounded-xl border-2 border-accent/40 px-4 text-lg"
            key={`item-${index + 1}`}
            value={`item-${index + 1}`}
          >
            <AccordionTrigger className="text-lg">
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="text-base text-neutral-500">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
