import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { IconRocket, IconSparkles } from '@tabler/icons-react';
import Link from 'next/link';
import { domain } from '@/lib/domain';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import FooterGradient from '../ui/footer-gradient';

export default function Footer() {
  return (
    <footer className="relative mt-20 h-[30rem] border-t py-10 md:h-[40rem]">
      <div className="absolute inset-0 z-[-1] bg-background bg-dot-black/[0.2] opacity-95 dark:bg-dot-white/[0.2]" />
      <FooterGradient />
      <div className="z-[20] flex h-full flex-col items-center justify-between">
        <div className="flex flex-col items-center justify-center">
          <Badge
            className="mb-4 flex w-fit items-center gap-2 rounded-full px-4 py-2"
            variant="outline"
          >
            Let&apos;s Start now <IconRocket className="size-4" />
          </Badge>
          <h1 className="text-center font-medium text-4xl md:text-6xl">
            Are you ready to explore your
            <span className="text-3xl text-muted-foreground/40 md:text-5xl">
              <br /> Creativity with{' '}
              <span className="mx-2 bg-gradient-to-tr from-white via-primary to-white bg-clip-text text-transparent">
                Superlogo
              </span>
            </span>
            ?
          </h1>
          <div className="mt-8">
            <SignedOut>
              <SignInButton
                forceRedirectUrl={`${domain}/generate`}
                signUpForceRedirectUrl={`${domain}/generate`}
              >
                <Button className="text-sm">
                  Start Generating <IconSparkles className="size-4" />
                </Button>
              </SignInButton>
            </SignedOut>
            <SignedIn>
              <Link className="w-full md:w-auto" href="/generate">
                <Button className="text-sm">
                  Start Generating <IconSparkles className="size-4" />
                </Button>
              </Link>
              {/* <Link href="/generate">
              <Button className="text-white">
                Get Started
              </Button> 
            </Link> */}
            </SignedIn>
          </div>
        </div>

        <div className="flex w-full border-border/40 pt-8">
          <div className="mx-auto flex w-full max-w-6xl items-center justify-between px-4">
            <div className="text-sm md:text-base">
              &copy; {new Date().getFullYear()} Superlogo
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
