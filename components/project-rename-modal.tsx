'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

interface ProjectRenameModalProps {
  projectName: string;
  projectId: string;
  open?: boolean;
  onProjectRenamed?: (oldName: string, newName: string) => void;
  onOpenChange?: (open: boolean) => void;
}

/**
 * Project rename modal component
 * Allows users to rename an existing project
 */
export function ProjectRenameModal({
  projectName,
  projectId: _projectId,
  open = false,
  onProjectRenamed,
  onOpenChange,
}: ProjectRenameModalProps) {
  const [newProjectName, setNewProjectName] = useState(projectName);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const trimmedName = newProjectName.trim();

    if (!trimmedName) {
      toast({
        title: 'Error',
        description: 'Project name is required',
        variant: 'destructive',
      });
      return;
    }

    if (trimmedName === projectName) {
      // No change needed
      onOpenChange?.(false);
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `/api/projects/${encodeURIComponent(projectName)}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ newName: trimmedName }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to rename project');
      }

      toast({
        title: 'Success',
        description: `Project renamed to "${trimmedName}"`,
      });

      // Reset form and close modal
      onOpenChange?.(false);

      // Callback with old and new names for updating parent state
      onProjectRenamed?.(projectName, trimmedName);
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to rename project',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setNewProjectName(projectName); // Reset to original name
    onOpenChange?.(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      setNewProjectName(projectName); // Reset to current name when opening
    }
    onOpenChange?.(newOpen);
  };

  return (
    <Dialog onOpenChange={handleOpenChange} open={open}>
      <DialogContent onClick={(e) => e.stopPropagation()}>
        <DialogTitle>Rename Project</DialogTitle>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Input
                autoFocus
                disabled={isLoading}
                onChange={(e) => setNewProjectName(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                onFocus={(e) => e.stopPropagation()}
                placeholder="Project name..."
                value={newProjectName}
              />
            </div>
            <div className="flex gap-3 pt-2">
              <Button
                disabled={isLoading}
                onClick={handleCancel}
                size="default"
                type="button"
                variant="secondary"
              >
                Cancel
              </Button>
              <Button
                disabled={
                  isLoading ||
                  !newProjectName.trim() ||
                  newProjectName.trim() === projectName
                }
                size="default"
                type="submit"
              >
                {isLoading ? 'Renaming...' : 'Rename Project'}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
