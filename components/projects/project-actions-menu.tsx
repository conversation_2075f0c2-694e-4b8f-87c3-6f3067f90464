'use client';

import { Edit, MoreVertical, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

/**
 * Compact actions dropdown for a project card.
 * Encapsulates trigger and menu items with proper event handling.
 *
 * @param props.isOpen - Controlled open state
 * @param props.onOpenChange - Callback when dropdown open changes
 * @param props.isDeleting - Disables delete when true and shows loading label
 * @param props.onRename - Invoked when user chooses rename
 * @param props.onDelete - Invoked when user confirms delete
 * @param props.buttonClassName - Optional classes for the trigger button
 * @example
 * <ProjectActionsMenu
 *   isOpen={open}
 *   onOpenChange={setOpen}
 *   isDeleting={false}
 *   onRename={() => ...}
 *   onDelete={() => ...}
 * />
 */
export function ProjectActionsMenu(props: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isDeleting: boolean;
  onRename: () => void;
  onDelete: () => void;
  buttonClassName?: string;
}) {
  const {
    isOpen,
    onOpenChange,
    isDeleting,
    onRename,
    onDelete,
    buttonClassName,
  } = props;

  return (
    <DropdownMenu onOpenChange={onOpenChange} open={isOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          className={buttonClassName}
          onClick={(e) => {
            e.stopPropagation();
          }}
          size="icon"
          variant="ghost"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            onRename();
          }}
        >
          <Edit className="mr-2 h-4 w-4" />
          Rename Project
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="text-destructive focus:text-destructive"
          disabled={isDeleting}
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {isDeleting ? 'Deleting...' : 'Delete Project'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
