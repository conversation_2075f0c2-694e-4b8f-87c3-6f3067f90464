'use client';

import { Plus } from 'lucide-react';
import Image from 'next/image';
import { FavoriteButton } from '@/components/projects/favorite-button';
import { ProjectActionsMenu } from '@/components/projects/project-actions-menu';
import { CardContent } from '@/components/ui/card';
import { formatDate } from '@/lib/projects';
import type { Project } from '@/types/project';

/**
 * Compact square grid card body for a project.
 * Purely presentational; all actions are passed via props.
 *
 * @param props.project - Project data to render
 * @param props.isDeleting - Whether a delete operation is in progress for this project
 * @param props.isDropdownOpen - Controls visibility of the actions dropdown
 * @param props.onToggleFavorite - Handler to toggle favorite state
 * @param props.onOpenRenameModal - Opens rename modal with project id/name
 * @param props.onDeleteProject - Confirms and deletes project
 * @param props.setDropdownOpen - Updates dropdown open state
 * @example
 * <GridProjectCard project={p} isDeleting={false} isDropdownOpen={false} onToggleFavorite={fn} onOpenRenameModal={fn} onDeleteProject={fn} setDropdownOpen={fn} />
 */
export function GridProjectCard(props: {
  project: Project;
  isDeleting: boolean;
  isDropdownOpen: boolean;
  onToggleFavorite: (project: Project) => void;
  onOpenRenameModal: (projectId: string, name: string) => void;
  onDeleteProject: (projectName: string, projectId: string) => void;
  setDropdownOpen: (open: boolean) => void;
}) {
  const {
    project,
    isDeleting,
    isDropdownOpen,
    onToggleFavorite,
    onOpenRenameModal,
    onDeleteProject,
    setDropdownOpen,
  } = props;

  return (
    <>
      <div className="relative">
        {project.thumbnail && project.thumbnail.trim() !== '' ? (
          <div className="aspect-square overflow-hidden">
            <Image
              alt={`${project.projectName} thumbnail`}
              className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
              height={256}
              src={project.thumbnail}
              width={256}
            />
          </div>
        ) : (
          <div className="flex aspect-square items-center justify-center bg-gradient-to-br from-muted/30 to-muted/60">
            <div className="flex flex-col items-center space-y-2 text-muted-foreground">
              <Plus className="h-6 w-6" />
              <span className="font-medium text-xs">Ready to create</span>
            </div>
          </div>
        )}

        {/* Actions menu */}
        <div className="absolute top-2 right-2">
          <ProjectActionsMenu
            buttonClassName="h-7 w-7 bg-background/80 opacity-0 backdrop-blur-sm transition-all duration-200 hover:bg-background/90 group-hover:opacity-100"
            isDeleting={isDeleting}
            isOpen={isDropdownOpen}
            onDelete={() => {
              setDropdownOpen(false);
              onDeleteProject(project.projectName, project.id);
            }}
            onOpenChange={setDropdownOpen}
            onRename={() => onOpenRenameModal(project.id, project.projectName)}
          />
        </div>

        {/* Favorite button */}
        <div className="absolute top-2 right-12">
          <FavoriteButton
            className="h-7 w-7 bg-background/80 opacity-0 backdrop-blur-sm transition-all duration-200 hover:bg-background/90 group-hover:opacity-100"
            iconSize={14}
            isFavorite={project.isFavorite}
            onClick={() => onToggleFavorite(project)}
          />
        </div>

        {/* Logo count badge */}
        {project.logoCount > 0 && (
          <div className="absolute top-2 left-2">
            <div className="rounded-full bg-primary px-1.5 py-0.5 font-medium text-[10px] text-primary-foreground backdrop-blur-sm">
              {project.logoCount} logo
              {project.logoCount === 1 ? '' : 's'}
            </div>
          </div>
        )}
      </div>

      <CardContent className="p-3">
        <div className="space-y-1.5">
          <h3 className="line-clamp-1 font-semibold text-base leading-tight">
            {project.projectName}
          </h3>

          {project.prompt ? (
            <p className="line-clamp-1 text-muted-foreground text-xs leading-relaxed">
              "{project.prompt}"
            </p>
          ) : (
            <p className="text-muted-foreground text-xs">
              {project.logoCount === 0
                ? 'Click to start creating logos'
                : 'Continue working on this project'}
            </p>
          )}

          <div className="flex items-center justify-between pt-0.5">
            <span className="text-[11px] text-muted-foreground">
              Created {formatDate(project.createdAt)}
            </span>
            {project.logoCount === 0 && (
              <div className="rounded-md bg-primary/10 px-1.5 py-0.5 font-medium text-[10px] text-primary">
                New
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </>
  );
}
