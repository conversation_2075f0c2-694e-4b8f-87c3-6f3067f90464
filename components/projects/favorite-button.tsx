'use client';

import { Star } from 'lucide-react';
import { Button } from '@/components/ui/button';

/**
 * Favorite toggle button used in project cards.
 * Stops event propagation to avoid triggering parent card clicks.
 *
 * @param props.isFavorite - Current favorite state
 * @param props.onClick - Click handler invoked after stopping propagation
 * @param props.iconSize - Star icon size in px (default 16)
 * @param props.className - Optional extra classes for the Button
 * @example
 * <FavoriteButton isFavorite onClick={() => doToggle()} iconSize={14} />
 */
export function FavoriteButton(props: {
  isFavorite: boolean;
  onClick: () => void;
  iconSize?: number;
  className?: string;
}) {
  const { isFavorite, onClick, iconSize = 16, className } = props;
  return (
    <Button
      aria-label={isFavorite ? 'Unfavorite project' : 'Favorite project'}
      className={className}
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      size="icon"
      variant="ghost"
    >
      <Star
        className={isFavorite ? 'text-yellow-500' : ''}
        fill={isFavorite ? 'currentColor' : 'none'}
        height={iconSize}
        width={iconSize}
      />
    </Button>
  );
}
