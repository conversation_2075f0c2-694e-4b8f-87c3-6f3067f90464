'use client';

import { Plus } from 'lucide-react';
import Image from 'next/image';
import { FavoriteButton } from '@/components/projects/favorite-button';
import { ProjectActionsMenu } from '@/components/projects/project-actions-menu';
import { formatDate } from '@/lib/projects';
import type { Project } from '@/types/project';

/**
 * Compact list row card body for a project.
 * Purely presentational; all actions are passed via props.
 */
export function ListProjectCard(props: {
  project: Project;
  isDeleting: boolean;
  isDropdownOpen: boolean;
  onToggleFavorite: (project: Project) => void;
  onOpenRenameModal: (projectId: string, name: string) => void;
  onDeleteProject: (projectName: string, projectId: string) => void;
  setDropdownOpen: (open: boolean) => void;
}) {
  const {
    project,
    isDeleting,
    isDropdownOpen,
    onToggleFavorite,
    onOpenRenameModal,
    onDeleteProject,
    setDropdownOpen,
  } = props;

  return (
    <div className="flex items-stretch gap-4 p-3">
      {/* Cover thumbnail */}
      <div className="relative">
        {project.thumbnail && project.thumbnail.trim() !== '' ? (
          <Image
            alt={`${project.projectName} thumbnail`}
            className="rounded-md object-cover"
            height={72}
            src={project.thumbnail}
            width={96}
          />
        ) : (
          <div className="flex h-[72px] w-[96px] items-center justify-center rounded-md bg-gradient-to-br from-muted/30 to-muted/60">
            <Plus className="h-5 w-5 text-muted-foreground" />
          </div>
        )}
        {project.logoCount > 0 && (
          <div className="-left-1 -top-1 absolute">
            <div className="rounded-full bg-primary px-1.5 py-0.5 font-medium text-[10px] text-primary-foreground">
              {project.logoCount}
            </div>
          </div>
        )}
      </div>

      {/* Text */}
      <div className="min-w-0 flex-1 self-center">
        <div className="flex items-center gap-2">
          <h3 className="line-clamp-1 font-semibold text-base leading-tight">
            {project.projectName}
          </h3>
        </div>
        {project.prompt && (
          <p className="line-clamp-1 text-muted-foreground text-xs">
            "{project.prompt}"
          </p>
        )}
        <span className="text-[11px] text-muted-foreground">
          Created {formatDate(project.createdAt)}
        </span>
      </div>

      {/* Actions on the right */}
      <div className="ml-auto flex items-center gap-1">
        <FavoriteButton
          iconSize={16}
          isFavorite={project.isFavorite}
          onClick={() => onToggleFavorite(project)}
        />
        <ProjectActionsMenu
          isDeleting={isDeleting}
          isOpen={isDropdownOpen}
          onDelete={() => {
            setDropdownOpen(false);
            onDeleteProject(project.projectName, project.id);
          }}
          onOpenChange={setDropdownOpen}
          onRename={() => onOpenRenameModal(project.id, project.projectName)}
        />
      </div>
    </div>
  );
}
