'use client';

import {
  type AnimationOptions,
  motion,
  stagger,
  useAnimate,
} from 'motion/react';
import { useState } from 'react';

interface TextProps {
  label: string;
  reverse?: boolean;
  transition?: AnimationOptions;
  staggerDuration?: number;
  staggerFrom?: 'first' | 'last' | 'center' | number;
  className?: string;
  onClick?: () => void;
}

const LetterSwapForward = ({
  label,
  reverse = true,
  transition = {
    type: 'spring',
    duration: 0.7,
  },
  staggerDuration = 0.03,
  staggerFrom = 'first',
  className,
  onClick,
  ...props
}: TextProps) => {
  const [scope, animate] = useAnimate();
  const [blocked, setBlocked] = useState(false);

  const hoverStart = () => {
    if (blocked) {
      return;
    }

    setBlocked(true);

    // Function to merge user transition with stagger and delay
    const mergeTransition = (baseTransition: AnimationOptions) => ({
      ...baseTransition,
      delay: stagger(staggerDuration, {
        from: staggerFrom,
      }),
    });

    animate(
      '.letter',
      { y: reverse ? '100%' : '-100%' },
      mergeTransition(transition)
    ).then(() => {
      animate(
        '.letter',
        {
          y: 0,
        },
        {
          duration: 0,
        }
      ).then(() => {
        setBlocked(false);
      });
    });

    animate(
      '.letter-secondary',
      {
        top: '0%',
      },
      mergeTransition(transition)
    ).then(() => {
      animate(
        '.letter-secondary',
        {
          top: reverse ? '-100%' : '100%',
        },
        {
          duration: 0,
        }
      );
    });
  };

  return (
    <button
      className={`relative flex items-center justify-center overflow-hidden border-0 bg-transparent p-0 ${className} `}
      onClick={onClick}
      onMouseEnter={hoverStart}
      ref={scope}
      type="button"
      {...props}
    >
      <span className="sr-only">{label}</span>

      {label.split('').map((letter: string, i: number) => {
        return (
          <span
            className="relative flex whitespace-pre"
            key={`${letter}-${i}-${label.length}`}
          >
            <motion.span className={'letter relative'} style={{ top: 0 }}>
              {letter}
            </motion.span>
            <motion.span
              aria-hidden={true}
              className="letter-secondary absolute"
              style={{ top: reverse ? '-100%' : '100%' }}
            >
              {letter}
            </motion.span>
          </span>
        );
      })}
    </button>
  );
};

export default LetterSwapForward;
