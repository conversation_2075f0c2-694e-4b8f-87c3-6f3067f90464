'use client';

import { IconMoonFilled, IconSunFilled } from '@tabler/icons-react';
import { useTheme } from 'next-themes';

export function ToggleTheme() {
  const { setTheme, theme } = useTheme();
  return (
    <button
      aria-label="Toggle theme"
      className="flex items-center gap-2 rounded-lg border-0 bg-transparent p-2 text-center transition-all duration-300"
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      type="button"
    >
      <IconSunFilled className="dark:-rotate-90 size-4 rotate-0 scale-100 transition-all dark:scale-0" />
      <IconMoonFilled className="absolute size-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
    </button>
  );
}
