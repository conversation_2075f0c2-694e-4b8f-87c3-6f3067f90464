import { IconClick, IconNorthStar } from '@tabler/icons-react';

export default function FloatingIcons() {
  return (
    <>
      <div className="-rotate-12 absolute top-[5%] left-[-2rem] flex items-center gap-2 rounded-2xl bg-primary/10 p-3 px-4 shadow-primary/30 shadow-xl backdrop-blur-sm md:top-[15rem] md:left-[17%] lg:top-[19rem] lg:left-[20%] dark:bg-accent/10">
        <IconNorthStar className="size-8 text-primary dark:text-foreground" />
      </div>
      <div className="absolute top-[48%] right-[-2rem] flex rotate-12 items-center gap-2 rounded-2xl bg-primary/10 p-3 px-4 shadow-primary/30 shadow-xl backdrop-blur-sm md:top-[15rem] md:right-[17%] lg:top-[19rem] lg:right-[20%] dark:bg-accent/10">
        <IconClick className="size-8 fill-[hsl(var(--primary))] text-primary dark:fill-[hsl(var(--foreground))] dark:text-foreground" />
      </div>
    </>
  );
}
