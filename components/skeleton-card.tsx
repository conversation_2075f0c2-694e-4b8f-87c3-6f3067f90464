import { Card, CardContent } from './ui/card';

export default function SkeletonCard() {
  return (
    <Card className="overflow-hidden rounded-2xl border bg-card/50 shadow-md backdrop-blur-sm">
      <div className="relative">
        <div className="aspect-[4/3] w-full animate-pulse bg-muted/60" />
      </div>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="h-5 w-3/4 animate-pulse rounded-lg bg-muted/60" />
          <div className="h-4 w-full animate-pulse rounded bg-muted/40" />
          <div className="h-4 w-2/3 animate-pulse rounded bg-muted/40" />
          <div className="flex items-center justify-between pt-1">
            <div className="h-3 w-1/3 animate-pulse rounded bg-muted/40" />
            <div className="h-4 w-8 animate-pulse rounded bg-muted/40" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
