'use client';

import {
  Content as AccordionPrimitiveContent,
  Header as AccordionPrimitiveHeader,
  Item as AccordionPrimitiveItem,
  Root as AccordionPrimitiveRoot,
  Trigger as AccordionPrimitiveTrigger,
} from '@radix-ui/react-accordion';
import { ChevronDown } from 'lucide-react';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from 'react';

import { cn } from '@/lib/utils';

// Create the namespace object for consistency
const AccordionPrimitive = {
  Root: AccordionPrimitiveRoot,
  Item: AccordionPrimitiveItem,
  Header: AccordionPrimitiveHeader,
  Trigger: AccordionPrimitiveTrigger,
  Content: AccordionPrimitiveContent,
};

/**
 * Accordion root. Thin alias to Radix `Accordion.Root` to preserve refs and `asChild`.
 *
 * @example
 * ```tsx
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Title</AccordionTrigger>
 *     <AccordionContent>Details</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 * ```
 */
const Accordion = AccordionPrimitive.Root;

/**
 * Accordion item wrapper.
 *
 * @param props - Props for Radix `Accordion.Item`.
 * @returns Styled `Accordion.Item` element.
 */
const AccordionItem = forwardRef<
  ElementRef<typeof AccordionPrimitive.Item>,
  ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item className={className} ref={ref} {...props} />
));
AccordionItem.displayName = 'AccordionItem';

/**
 * Accordion trigger control. Wraps Radix `Accordion.Header` + `Accordion.Trigger` and forwards refs.
 *
 * @param props - Props for Radix `Accordion.Trigger`.
 * @returns Styled `Accordion.Trigger` element.
 */
const AccordionTrigger = forwardRef<
  ElementRef<typeof AccordionPrimitive.Trigger>,
  ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      className={cn(
        'flex flex-1 items-center justify-between py-4 text-left font-medium text-sm transition-all [&[data-state=open]>svg]:rotate-180',
        className
      )}
      ref={ref}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

/**
 * Accordion content region with open/close height animations.
 *
 * @param props - Props for Radix `Accordion.Content`.
 * @returns Styled `Accordion.Content` element.
 */
const AccordionContent = forwardRef<
  ElementRef<typeof AccordionPrimitive.Content>,
  ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    ref={ref}
    {...props}
  >
    <div className={cn('pt-0 pb-4', className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
