'use client';

import Image from 'next/image';

interface LogoImageProps {
  src: string;
  index: number;
}

export function LogoImage({ src, index }: LogoImageProps) {
  return (
    <div className="group relative aspect-[4/3] h-full overflow-hidden rounded-xl">
      {src && src.trim() !== '' ? (
        <Image
          alt={`Artwork ${index + 1}`}
          className="object-cover transition-all duration-700 ease-in-out group-hover:scale-110 group-hover:brightness-110"
          fill
          priority={index < 4}
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          src={src}
        />
      ) : (
        <div className="flex h-full w-full items-center justify-center bg-muted/20">
          <span className="text-muted-foreground text-sm">No image</span>
        </div>
      )}
      <div className="absolute inset-0 bg-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
    </div>
  );
}
