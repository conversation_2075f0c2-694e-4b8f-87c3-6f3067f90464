'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Badge } from './badge';

interface CreditsData {
  credits: number | null;
  monthlyLimit: number | null;
  plan: string | null;
}

interface CreditsPillProps {
  compact?: boolean;
}

/**
 * Compact credits pill that fetches the user's remaining credits and shows an Upgrade hint when low.
 */
export function CreditsPill({ compact }: CreditsPillProps) {
  const [data, setData] = useState<CreditsData>({
    credits: null,
    monthlyLimit: null,
    plan: null,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const applyPayload = (payload: {
      credits?: number | null;
      remaining?: number | null;
      monthlyLimit?: number | null;
      plan?: string | null;
    }) => {
      if (!isMounted) {
        return;
      }
      let creditsValue: number | null = null;
      if (typeof payload.credits === 'number') {
        creditsValue = payload.credits;
      } else if (typeof payload.remaining === 'number') {
        creditsValue = payload.remaining;
      }
      const monthly =
        typeof payload.monthlyLimit === 'number' ? payload.monthlyLimit : null;
      const planValue = payload.plan ?? null;
      setData({
        credits: creditsValue,
        monthlyLimit: monthly,
        plan: planValue,
      });
      setLoading(false);
    };

    const fetchCredits = async () => {
      try {
        const res = await fetch('/api/credits');
        if (!res.ok) {
          return;
        }
        const json = (await res.json()) as {
          credits?: number;
          remaining?: number;
          monthlyLimit?: number;
          plan?: string;
        };
        applyPayload(json);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Initial fetch only when authenticated; if not, keep pill hidden via parent
    fetchCredits();

    // Listen for app-wide updates from generator
    const onCreditsUpdate = (e: Event) => {
      try {
        const detail = (e as CustomEvent).detail as {
          credits?: number;
          monthlyLimit?: number;
          plan?: string;
        };
        if (detail) {
          applyPayload(detail);
        }
      } catch {
        // ignore
      }
    };
    window.addEventListener('credits:update', onCreditsUpdate as EventListener);

    // Fallback: listen to storage updates (other tabs)
    const onStorage = (e: StorageEvent) => {
      if (e.key !== 'superlogo:credits:last' || !e.newValue) {
        return;
      }
      try {
        const cached = JSON.parse(e.newValue) as {
          credits?: number;
          monthlyLimit?: number;
          plan?: string;
        };
        applyPayload(cached);
      } catch {
        // ignore
      }
    };
    window.addEventListener('storage', onStorage);

    // Hydrate from cache if present
    try {
      const raw = localStorage.getItem('superlogo:credits:last');
      if (raw) {
        const cached = JSON.parse(raw) as {
          credits?: number;
          monthlyLimit?: number;
          plan?: string;
        };
        applyPayload(cached);
      }
    } catch {
      // ignore
    }

    return () => {
      isMounted = false;
      window.removeEventListener(
        'credits:update',
        onCreditsUpdate as EventListener
      );
      window.removeEventListener('storage', onStorage);
    };
  }, []);

  const { credits, monthlyLimit, plan } = data;
  const threshold =
    typeof monthlyLimit === 'number'
      ? Math.max(5, Math.floor(monthlyLimit * 0.1))
      : 5;
  const isLow = typeof credits === 'number' && credits <= threshold;

  const creditsText = (() => {
    if (loading) {
      return '—';
    }
    if (typeof credits === 'number') {
      return String(credits);
    }
    return '—';
  })();

  // We no longer display monthlyLimit to avoid confusing "— / N" states

  return (
    <div className="flex items-center gap-2 rounded-full border px-3 py-1 text-xs">
      <span className="opacity-70">Credits</span>
      <span className="font-semibold">{creditsText}</span>
      {!compact && plan ? <span className="opacity-70">· {plan}</span> : null}
      {isLow ? (
        <Link className="ml-1" href="/pricing">
          <Badge variant="secondary">Upgrade</Badge>
        </Link>
      ) : null}
    </div>
  );
}
