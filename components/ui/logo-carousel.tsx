'use client';

import Autoplay from 'embla-carousel-autoplay';
import { useRef } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel';
import { artworkData } from '@/constants/data';
import { LogoImage } from './logo-image';

export function LogoCarousel() {
  const plugin = useRef(
    Autoplay({
      delay: 0, // No delay for continuous movement
      stopOnInteraction: false,
      stopOnMouseEnter: false,
      stopOnFocusIn: false,
    })
  );

  const duplicatedArtworks = artworkData.map((item) => item.imageUrl);

  return (
    <section className="relative mx-auto mt-20 mb-10 h-fit w-full max-w-7xl overflow-hidden md:mt-36">
      <Carousel
        className="w-full"
        opts={{
          align: 'start',
          loop: true,
          skipSnaps: true,
          dragFree: true,
          containScroll: 'trimSnaps',
          duration: 10_000,
        }}
        plugins={[plugin.current]}
      >
        <CarouselContent className="animate-scroll">
          {duplicatedArtworks.map((artwork, index) => {
            // Extract unique ID from the URL for stable key
            const urlId =
              artwork.split('/').pop()?.split('_')[0] || `artwork-${index}`;
            return (
              <CarouselItem
                className="basis-[50%] sm:basis-1/2 md:basis-1/3 lg:basis-[30%]"
                key={urlId}
              >
                <LogoImage index={index} src={artwork} />
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </section>
  );
}
