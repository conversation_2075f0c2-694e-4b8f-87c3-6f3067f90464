'use client';

import {
  Arrow as TooltipArrow,
  Content as TooltipContentPrimitive,
  Portal as TooltipPortal,
  Provider as TooltipProviderPrimitive,
  Root as TooltipRoot,
  Trigger as TooltipTriggerPrimitive,
} from '@radix-ui/react-tooltip';
import type * as React from 'react';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from 'react';

import { cn } from '@/lib/utils';

/**
 * Tooltip provider that configures show delay for nested tooltips.
 *
 * @param delayDuration - Delay before showing a tooltip in ms (default 0).
 * @param props - All provider props from Radix.
 * @returns Radix `Tooltip.Provider` element.
 * @throws None
 */
function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipProviderPrimitive>) {
  return (
    <TooltipProviderPrimitive
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  );
}

/**
 * Tooltip root. Wraps Radix `Tooltip.Root` with a provider.
 * Keeps `asChild` polymorphism.
 *
 * @returns Radix `Tooltip.Root` wrapped by `TooltipProvider`.
 * @throws None
 */
function Tooltip({ ...props }: React.ComponentProps<typeof TooltipRoot>) {
  return (
    <TooltipProvider>
      <TooltipRoot data-slot="tooltip" {...props} />
    </TooltipProvider>
  );
}

/**
 * Tooltip trigger. Forwards refs to the underlying Radix trigger.
 *
 * @returns Styled `Tooltip.Trigger` element.
 * @throws None
 */
const TooltipTrigger = forwardRef<
  ElementRef<typeof TooltipTriggerPrimitive>,
  ComponentPropsWithoutRef<typeof TooltipTriggerPrimitive>
>(({ ...props }, ref) => (
  <TooltipTriggerPrimitive data-slot="tooltip-trigger" ref={ref} {...props} />
));
TooltipTrigger.displayName = TooltipTriggerPrimitive.displayName;

/**
 * Tooltip content surface. Forwards refs and applies
 * data-attribute driven animations and transform-origin per Radix.
 *
 * @returns Styled `Tooltip.Content` element.
 * @throws None
 */
const TooltipContent = forwardRef<
  ElementRef<typeof TooltipContentPrimitive>,
  ComponentPropsWithoutRef<typeof TooltipContentPrimitive>
>(({ className, sideOffset = 0, children, ...props }, ref) => (
  <TooltipPortal>
    <TooltipContentPrimitive
      className={cn(
        'fade-in-0 zoom-in-95 data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-[var(--radix-tooltip-content-transform-origin)] animate-in text-balance rounded-md bg-primary px-3 py-1.5 text-primary-foreground text-xs data-[state=closed]:animate-out',
        className
      )}
      data-slot="tooltip-content"
      ref={ref}
      sideOffset={sideOffset}
      {...props}
    >
      {children}
      <TooltipArrow className="z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px] bg-primary fill-primary" />
    </TooltipContentPrimitive>
  </TooltipPortal>
));
TooltipContent.displayName = TooltipContentPrimitive.displayName;

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
