'use client';

import {
  Content as TabsContentPrimitive,
  List as TabsListPrimitive,
  Root as TabsRoot,
  Trigger as TabsTriggerPrimitive,
} from '@radix-ui/react-tabs';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from 'react';
import { cn } from '@/lib/utils';

/**
 * Tabs root. Thin alias to <PERSON><PERSON><PERSON> `Tabs.Root` to preserve ref forwarding and `asChild`.
 *
 * @example
 * ```tsx
 * <Tabs defaultValue="account">
 *   <TabsList>
 *     <TabsTrigger value="account">Account</TabsTrigger>
 *     <TabsTrigger value="password">Password</TabsTrigger>
 *   </TabsList>
 *   <TabsContent value="account" />
 * </Tabs>
 * ```
 */
const Tabs = TabsRoot;

/**
 * Tabs list container. Forwards refs and styles the list element.
 *
 * @param props - All props supported by <PERSON>di<PERSON> `Tabs.List`.
 * @returns Styled `Tabs.List` element.
 * @example
 * ```tsx
 * <TabsList className="mb-2">...</TabsList>
 * ```
 */
const TabsList = forwardRef<
  ElementRef<typeof TabsListPrimitive>,
  ComponentPropsWithoutRef<typeof TabsListPrimitive>
>(({ className, ...props }, ref) => (
  <TabsListPrimitive
    className={cn(
      'inline-flex h-9 items-center rounded-lg bg-muted p-1 text-muted-foreground',
      className
    )}
    ref={ref}
    {...props}
  />
));
TabsList.displayName = TabsListPrimitive.displayName;

/**
 * Tabs trigger. Forwards refs and applies active/focus styles.
 *
 * @param props - All props supported by Radix `Tabs.Trigger`.
 * @returns Styled `Tabs.Trigger` element.
 * @example
 * ```tsx
 * <TabsTrigger value="account">Account</TabsTrigger>
 * ```
 */
const TabsTrigger = forwardRef<
  ElementRef<typeof TabsTriggerPrimitive>,
  ComponentPropsWithoutRef<typeof TabsTriggerPrimitive>
>(({ className, ...props }, ref) => (
  <TabsTriggerPrimitive
    className={cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 font-medium text-sm transition-all',
      'focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring',
      'disabled:pointer-events-none disabled:opacity-50',
      'data-[state=active]:bg-background data-[state=active]:text-foreground',
      className
    )}
    ref={ref}
    {...props}
  />
));
TabsTrigger.displayName = TabsTriggerPrimitive.displayName;

/**
 * Tabs content. Forwards refs and adds a small top margin.
 *
 * @param props - All props supported by Radix `Tabs.Content`.
 * @returns Styled `Tabs.Content` element.
 * @example
 * ```tsx
 * <TabsContent value="account">...</TabsContent>
 * ```
 */
const TabsContent = forwardRef<
  ElementRef<typeof TabsContentPrimitive>,
  ComponentPropsWithoutRef<typeof TabsContentPrimitive>
>(({ className, ...props }, ref) => (
  <TabsContentPrimitive
    className={cn('mt-2', className)}
    ref={ref}
    {...props}
  />
));
TabsContent.displayName = TabsContentPrimitive.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
