import type { TextareaHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

/**
 * Textarea
 *
 * A Tailwind-styled textarea that follows the app's design tokens.
 * Uses native <textarea> for full control and consistency with other inputs.
 *
 * @param props - All native textarea props including className
 * @returns JSX.Element
 * @example
 * <Textarea placeholder="Write something..." rows={4} />
 */
function Textarea({
  className,
  ...props
}: TextareaHTMLAttributes<HTMLTextAreaElement>) {
  return (
    <textarea
      className={cn(
        'flex min-h-20 w-full rounded-md border bg-transparent px-3 py-2 text-sm',
        'placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring',
        'disabled:cursor-not-allowed disabled:opacity-50',
        className
      )}
      {...props}
    />
  );
}

export { Textarea };
