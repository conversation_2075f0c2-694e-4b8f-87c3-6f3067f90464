'use client';

import {
  Action as ToastPrimitiveAction,
  Close as ToastPrimitiveClose,
  Description as ToastPrimitiveDescription,
  Provider as ToastPrimitiveProvider,
  Root as ToastPrimitiveRoot,
  Title as ToastPrimitiveTitle,
  Viewport as ToastPrimitiveViewport,
} from '@radix-ui/react-toast';
import { cva, type VariantProps } from 'class-variance-authority';
import { X } from 'lucide-react';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
  type ReactElement,
} from 'react';

import { cn } from '@/lib/utils';

// Create the namespace object for consistency
const ToastPrimitives = {
  Provider: ToastPrimitiveProvider,
  Viewport: ToastPrimitiveViewport,
  Root: ToastPrimitiveRoot,
  Action: ToastPrimitiveAction,
  Close: ToastPrimitiveClose,
  Title: ToastPrimitiveTitle,
  Description: ToastPrimitiveDescription,
};

/**
 * Toast provider. Controls portal and announcements for toasts.
 *
 * @returns Radix `Toast.Provider` element.
 * @example
 * ```tsx
 * <ToastProvider>
 *   <ToastViewport />
 * </ToastProvider>
 * ```
 */
const ToastProvider = ToastPrimitives.Provider;

/**
 * Toast viewport container where toasts render.
 * Forwards refs.
 *
 * @param props - Props for Radix `Toast.Viewport`.
 * @returns Styled `Toast.Viewport` element.
 */
const ToastViewport = forwardRef<
  ElementRef<typeof ToastPrimitives.Viewport>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    className={cn(
      'fixed bottom-0 z-[100] flex max-h-screen w-full flex-col gap-2 p-4 sm:right-0 md:max-w-[420px]',
      className
    )}
    ref={ref}
    {...props}
  />
));
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
  'group data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-bottom-full pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-lg border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=closed]:animate-out data-[state=open]:animate-in data-[swipe=end]:animate-out data-[swipe=move]:transition-none',
  {
    variants: {
      variant: {
        default:
          'border-border bg-background text-foreground shadow-lg shadow-primary/10',
        destructive:
          'destructive group border-destructive bg-destructive/10 text-destructive shadow-destructive/10 shadow-lg',
        success:
          'border-green-500 bg-green-500/10 text-green-500 shadow-green-500/10 shadow-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

/**
 * Toast root component with variant support.
 * Forwards refs and exposes state-driven animations via data attributes.
 *
 * @param props - Props for Radix `Toast.Root` plus `variant`.
 * @returns Styled `Toast.Root` element.
 */
const Toast = forwardRef<
  ElementRef<typeof ToastPrimitives.Root>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      className={cn(toastVariants({ variant }), className)}
      ref={ref}
      {...props}
    />
  );
});
Toast.displayName = ToastPrimitives.Root.displayName;

/**
 * Toast action button.
 * Forwards refs and adapts styles based on toast variant via group selectors.
 *
 * @param props - Props for Radix `Toast.Action`.
 * @returns Styled `Toast.Action` element.
 */
const ToastAction = forwardRef<
  ElementRef<typeof ToastPrimitives.Action>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    className={cn(
      'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 font-medium text-sm transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:focus:ring-destructive group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground',
      className
    )}
    ref={ref}
    {...props}
  />
));
ToastAction.displayName = ToastPrimitives.Action.displayName;

/**
 * Toast close button.
 * Forwards refs and adapts styles based on toast variant via group selectors.
 *
 * @param props - Props for Radix `Toast.Close`.
 * @returns Styled `Toast.Close` element.
 */
const ToastClose = forwardRef<
  ElementRef<typeof ToastPrimitives.Close>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    className={cn(
      'absolute top-1 right-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-destructive-foreground/50 group-[.success]:text-green-500/50 group-[.destructive]:hover:text-destructive-foreground group-[.success]:hover:text-green-500',
      className
    )}
    ref={ref}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
));
ToastClose.displayName = ToastPrimitives.Close.displayName;

/**
 * Toast title text.
 * Forwards refs.
 *
 * @param props - Props for Radix `Toast.Title`.
 * @returns Styled `Toast.Title` element.
 */
const ToastTitle = forwardRef<
  ElementRef<typeof ToastPrimitives.Title>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    className={cn(
      'font-semibold text-sm leading-none tracking-tight',
      className
    )}
    ref={ref}
    {...props}
  />
));
ToastTitle.displayName = ToastPrimitives.Title.displayName;

/**
 * Toast description text.
 * Forwards refs.
 *
 * @param props - Props for Radix `Toast.Description`.
 * @returns Styled `Toast.Description` element.
 */
const ToastDescription = forwardRef<
  ElementRef<typeof ToastPrimitives.Description>,
  ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    className={cn('text-sm leading-relaxed opacity-90', className)}
    ref={ref}
    {...props}
  />
));
ToastDescription.displayName = ToastPrimitives.Description.displayName;

type ToastProps = ComponentPropsWithoutRef<typeof Toast>;

type ToastActionElement = ReactElement<typeof ToastAction>;

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
};
