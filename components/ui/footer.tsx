import Link from 'next/link';

const Footer = () => {
  const _footerSections = [
    {
      title: 'Product',
      links: ['Features', 'Pricing', 'Security', 'Updates'],
    },
    {
      title: 'Company',
      links: ['About', 'Blog', 'Careers', 'Press'],
    },
    {
      title: 'Resources',
      links: ['Documentation', 'Help Center', 'Contact', 'Status'],
    },
    {
      title: 'Legal',
      links: ['Privacy', 'Terms', 'Security', 'Cookies'],
    },
  ];

  return (
    <footer className="bg-primary px-6 py-16 text-white">
      <div className="flex flex-col items-center justify-between gap-4 border-white/10 border-t pt-8 md:flex-row">
        <p className="text-sm text-white/70">
          © 2024 BankApp. All rights reserved.
        </p>
        <div className="flex items-center space-x-6">
          <Link
            className="text-white/70 transition-colors hover:text-white"
            href="#"
          >
            Twitter
          </Link>
          <Link
            className="text-white/70 transition-colors hover:text-white"
            href="#"
          >
            LinkedIn
          </Link>
          <Link
            className="text-white/70 transition-colors hover:text-white"
            href="#"
          >
            GitHub
          </Link>
        </div>
      </div>
      {/* intentionally minimal footer */}
    </footer>
  );
};

export default Footer;
