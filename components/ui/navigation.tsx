import { SignedIn, SignedOut, SignIn<PERSON><PERSON>on, User<PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import Link from 'next/link';
import { domain } from '@/lib/domain';
import { Button } from './button';
import { CreditsPill } from './credits-pill';

const Navigation = () => {
  return (
    <nav className="fixed top-0 right-0 left-0 z-50 border-border border-b bg-background/80 backdrop-blur-lg">
      <div className="mx-auto flex h-16 max-w-7xl items-center justify-between px-6">
        <Link className="font-semibold text-xl" href="/">
          Superlogo
        </Link>

        <div className="hidden items-center space-x-8 md:flex">
          <Link
            className="text-gray-500 transition-colors hover:text-primary"
            href="/projects"
          >
            Projects
          </Link>
          <Link
            className="text-gray-500 transition-colors hover:text-primary"
            href="/generate"
          >
            Generate
          </Link>
          <Link
            className="text-gray-500 transition-colors hover:text-primary"
            href="/pricing"
          >
            Pricing
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <SignedIn>
            <CreditsPill compact />
          </SignedIn>
          <Link href="/pricing">
            <Button
              className="hidden text-white md:inline-flex"
              variant="default"
            >
              Upgrade
            </Button>
          </Link>
          <SignedOut>
            <SignInButton
              forceRedirectUrl={`${domain}/generate`}
              signUpForceRedirectUrl={`${domain}/generate`}
            >
              <Button className="inline-flex text-white">Sign In</Button>
            </SignInButton>
          </SignedOut>
          <SignedIn>
            <UserButton />
            {/* <Link href="/generate">
              <Button className="text-white">
                Get Started
              </Button> 
            </Link> */}
          </SignedIn>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
