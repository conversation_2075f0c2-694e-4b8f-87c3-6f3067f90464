import type { InputHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

/**
 * Input
 *
 * Tailwind-styled input that merges custom className via cn().
 *
 * @param props - Native input props with optional className
 */
function Input({ className, ...props }: InputHTMLAttributes<HTMLInputElement>) {
  return (
    <input
      className={cn(
        'h-9 w-full rounded-md border bg-transparent px-3 text-sm',
        'placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring',
        'disabled:cursor-not-allowed disabled:opacity-50',
        className
      )}
      {...props}
    />
  );
}

export { Input };
