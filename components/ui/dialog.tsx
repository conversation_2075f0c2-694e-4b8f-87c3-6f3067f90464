'use client';

import {
  Close as DialogClosePrimitive,
  Content as DialogContentPrimitive,
  Description as DialogDescriptionPrimitive,
  Overlay as DialogOverlay,
  Portal as DialogPortal,
  Root as DialogRoot,
  Title as DialogTitlePrimitive,
  Trigger as DialogTriggerPrimitive,
} from '@radix-ui/react-dialog';
import type * as React from 'react';
import { cn } from '@/lib/utils';

/**
 * Dialog root. Thin alias over <PERSON>di<PERSON>'s `Dialog.Root` to preserve ref forwarding and `asChild`.
 *
 * @param props - All props from `@radix-ui/react-dialog` `Root`.
 * @returns The dialog root provider.
 * @example
 * <Dialog>
 *   <DialogTrigger asChild>
 *     <button>Open</button>
 *   </DialogTrigger>
 *   <DialogContent>...</DialogContent>
 * </Dialog>
 */
const Dialog = DialogRoot;

/**
 * Dialog trigger. Aliased to Radix `Dialog.Trigger` to keep ref forwarding intact.
 *
 * @param props - All trigger props including `asChild` for polymorphism.
 * @returns The trigger element.
 */
const DialogTrigger = DialogTriggerPrimitive;

/** Dialog content with overlay and transitions */
function DialogContent({
  className,
  ...props
}: React.ComponentProps<typeof DialogContentPrimitive>) {
  return (
    <DialogPortal>
      <DialogOverlay
        className={cn(
          'fixed inset-0 z-50 bg-black/50',
          'data-[state=closed]:fade-out-0 data-[state=closed]:animate-out',
          'data-[state=open]:fade-in-0 data-[state=open]:animate-in'
        )}
      />
      <DialogContentPrimitive
        className={cn(
          '-translate-x-1/2 -translate-y-1/2 fixed top-1/2 left-1/2 z-50 grid w-full max-w-lg gap-4 rounded-lg border bg-background p-6 shadow-lg duration-200',
          'data-[state=closed]:zoom-out-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:animate-out',
          'data-[state=open]:zoom-in-95 data-[state=open]:slide-in-from-left-1/2 data-[state=open]:animate-in',
          className
        )}
        {...props}
      />
    </DialogPortal>
  );
}

/**
 * Dialog title. Adds typography classes while preserving Radix semantics.
 *
 * @param className - Additional class names.
 * @param props - All props from Radix `Title`.
 * @returns The styled title element.
 * @example
 * <DialogTitle>Confirm action</DialogTitle>
 */
function DialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof DialogTitlePrimitive>) {
  return (
    <DialogTitlePrimitive
      className={cn(
        'font-semibold text-lg leading-none tracking-tight',
        className
      )}
      {...props}
    />
  );
}

/**
 * Dialog description. Secondary text under the title.
 *
 * @param className - Additional class names.
 * @param props - All props from Radix `Description`.
 * @returns The styled description element.
 */
function DialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogDescriptionPrimitive>) {
  return (
    <DialogDescriptionPrimitive
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
}

/**
 * Dialog close. Aliased to Radix `Dialog.Close` to preserve ref forwarding.
 *
 * @param props - All close props; supports `asChild`.
 */
const DialogClose = DialogClosePrimitive;

export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogClose,
};
