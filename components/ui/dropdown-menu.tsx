'use client';

import {
  Content as DropdownMenuContentPrimitive,
  Item as DropdownMenuItemPrimitive,
  Root as DropdownMenuRoot,
  Separator as DropdownMenuSeparatorPrimitive,
  Trigger as DropdownMenuTriggerPrimitive,
} from '@radix-ui/react-dropdown-menu';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from 'react';
import { cn } from '@/lib/utils';

/**
 * Dropdown menu root. Thin alias to Radix `DropdownMenu.Root` to preserve refs and `asChild`.
 *
 * @example
 * ```tsx
 * <DropdownMenu>
 *   <DropdownMenuTrigger asChild>
 *     <button>Open</button>
 *   </DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Item</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 * ```
 */
const DropdownMenu = DropdownMenuRoot;

/**
 * Dropdown trigger. Aliased to Radix to keep ref forwarding intact.
 *
 * @returns Radix `DropdownMenu.Trigger` element.
 */
const DropdownMenuTrigger = DropdownMenuTriggerPrimitive;

/**
 * Dropdown content surface. Forwards refs and applies styling.
 *
 * @param props - Props for Radix `DropdownMenu.Content`.
 * @returns Styled `DropdownMenu.Content` element.
 */
const DropdownMenuContent = forwardRef<
  ElementRef<typeof DropdownMenuContentPrimitive>,
  ComponentPropsWithoutRef<typeof DropdownMenuContentPrimitive>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuContentPrimitive
    className={cn(
      'z-50 min-w-40 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md',
      className
    )}
    ref={ref}
    sideOffset={sideOffset}
    {...props}
  />
));
DropdownMenuContent.displayName = DropdownMenuContentPrimitive.displayName;

/**
 * Dropdown item. Forwards refs and applies focus/disabled styles.
 *
 * @param props - Props for Radix `DropdownMenu.Item`.
 * @returns Styled `DropdownMenu.Item` element.
 */
const DropdownMenuItem = forwardRef<
  ElementRef<typeof DropdownMenuItemPrimitive>,
  ComponentPropsWithoutRef<typeof DropdownMenuItemPrimitive>
>(({ className, ...props }, ref) => (
  <DropdownMenuItemPrimitive
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm px-2.5 py-1.5 text-sm outline-none',
      'focus:bg-accent focus:text-accent-foreground',
      'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className
    )}
    ref={ref}
    {...props}
  />
));
DropdownMenuItem.displayName = DropdownMenuItemPrimitive.displayName;

/**
 * Dropdown separator. Forwards refs for consistency.
 *
 * @returns Styled `DropdownMenu.Separator` element.
 */
const DropdownMenuSeparator = forwardRef<
  ElementRef<typeof DropdownMenuSeparatorPrimitive>,
  ComponentPropsWithoutRef<typeof DropdownMenuSeparatorPrimitive>
>(({ className, ...props }, ref) => (
  <DropdownMenuSeparatorPrimitive
    className={cn('-mx-1 my-1 h-px bg-border', className)}
    ref={ref}
    {...props}
  />
));
DropdownMenuSeparator.displayName = DropdownMenuSeparatorPrimitive.displayName;

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
};
