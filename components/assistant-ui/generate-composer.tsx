'use client';

import { ComposerPrimitive } from '@assistant-ui/react';
import {
  ArrowUpIcon,
  Cpu,
  Image as ImageIcon,
  PlusIcon,
  Sparkles,
  Square,
} from 'lucide-react';
import { useState } from 'react';
import { MODEL_OPTIONS, SIZE_OPTIONS } from '@/app/generate/constants';
import type { ModelType, QualityType, SizeType } from '@/app/generate/types';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface GenerateComposerProps {
  /**
   * Called when the user submits a prompt.
   */
  onGenerate: (prompt: string) => void;
  /**
   * When true, UI is disabled and shows generating state.
   */
  isGenerating: boolean;
  /**
   * Optional starting prompt value used to prefill the input when deep-linking from project details.
   */
  initialPrompt?: string;
  /** Current model selection shown and controlled by toolbar */
  selectedModel: ModelType;
  /** Setter for model selection */
  onChangeModel: (value: ModelType) => void;
  /** Current size (square dimension) selection */
  selectedSize: SizeType;
  /** Setter for size selection */
  onChangeSize: (value: SizeType) => void;
  /** Current quality selection */
  selectedQuality: QualityType;
  /** Setter for quality selection */
  onChangeQuality: (value: QualityType) => void;
}

/**
 * Minimal Assistant UI composer wired to our generation flow
 * Preserves UX behavior (auto-resize, keyboard, a11y) without chat threading.
 */
export function GenerateComposer({
  onGenerate,
  isGenerating,
  initialPrompt,
  selectedModel,
  onChangeModel,
  selectedSize,
  onChangeSize,
  selectedQuality,
  onChangeQuality,
}: GenerateComposerProps) {
  const [value, setValue] = useState(initialPrompt ?? '');

  const submit = () => {
    const v = value.trim();
    if (v && !isGenerating) {
      onGenerate(v);
      setValue('');
    }
  };

  return (
    <div className="pointer-events-auto">
      <div className="relative mx-auto w-full max-w-3xl bg-background px-0 pb-2 md:pb-3">
        <ComposerPrimitive.Root className="relative flex w-full flex-col rounded-2xl focus-within:ring-2 focus-within:ring-black focus-within:ring-offset-2 dark:focus-within:ring-white">
          <ComposerPrimitive.Input
            aria-label="Message input"
            autoFocus={false}
            className={cn(
              'min-h-12',
              'w-full',
              'max-h-24',
              'resize-none',
              'rounded-t-2xl',
              'border-x',
              'border-t',
              'border-border',
              'bg-muted',
              'px-3',
              'pb-2',
              'pt-2',
              'text-sm',
              'outline-none',
              'placeholder:text-muted-foreground',
              'dark:border-muted-foreground/15'
            )}
            disabled={isGenerating}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submit();
              }
            }}
            placeholder={
              isGenerating
                ? 'Creating your logo…'
                : 'What do you want to create today?'
            }
            rows={1}
            value={value}
          />
          <div
            className={cn(
              'relative',
              'flex',
              'items-center',
              'justify-between',
              'rounded-b-2xl',
              'border-x',
              'border-b',
              'border-border',
              'bg-muted',
              'p-2',
              'dark:border-muted-foreground/15'
            )}
          >
            <div className="flex items-center gap-1.5">
              <Button
                aria-label="Attach (coming soon)"
                className="hover:bg-foreground/10 dark:hover:bg-background/50"
                disabled
                size="icon"
                type="button"
                variant="ghost"
              >
                <PlusIcon className="size-4" />
              </Button>

              {/* Toolbar controls: Model, Size, Quality */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    aria-label="Select model"
                    className="h-8 gap-1 rounded-md border border-muted-foreground/30 px-2 text-xs hover:bg-foreground/10 dark:border-muted-foreground/50 dark:hover:bg-background/50"
                    disabled={isGenerating}
                    type="button"
                    variant="ghost"
                  >
                    <Cpu className="size-4" />
                    <span className="hidden capitalize sm:inline">
                      {MODEL_OPTIONS.find((m) => m.id === selectedModel)
                        ?.name ?? 'Model'}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  {MODEL_OPTIONS.map((m) => (
                    <DropdownMenuItem
                      key={m.id}
                      onClick={() => onChangeModel(m.id as ModelType)}
                    >
                      {m.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    aria-label="Select size"
                    className="h-8 gap-1 rounded-md border border-muted-foreground/30 px-2 text-xs hover:bg-foreground/10 dark:border-muted-foreground/50 dark:hover:bg-background/50"
                    disabled={isGenerating}
                    type="button"
                    variant="ghost"
                  >
                    <ImageIcon className="size-4" />
                    <span className="hidden sm:inline">{selectedSize}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  {SIZE_OPTIONS.map((s) => (
                    <DropdownMenuItem
                      key={s.id}
                      onClick={() => onChangeSize(s.id as SizeType)}
                    >
                      {s.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    aria-label="Select quality"
                    className="h-8 gap-1 rounded-md border border-muted-foreground/30 px-2 text-xs hover:bg-foreground/10 dark:border-muted-foreground/50 dark:hover:bg-background/50"
                    disabled={isGenerating}
                    type="button"
                    variant="ghost"
                  >
                    <Sparkles className="size-4" />
                    <span className="hidden capitalize sm:inline">
                      {selectedQuality}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-40">
                  {(['standard', 'hd'] as QualityType[]).map((q) => (
                    <DropdownMenuItem
                      key={q}
                      onClick={() => onChangeQuality(q)}
                    >
                      {q}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {isGenerating ? (
              <Button
                aria-label="Stop generating"
                className="size-8 rounded-full border border-muted-foreground/60 dark:border-muted-foreground/90"
                disabled
                size="icon"
                type="button"
                variant="default"
              >
                <Square className="size-3.5 fill-white dark:size-4 dark:fill-black" />
              </Button>
            ) : (
              <Button
                aria-label="Send message"
                className="size-8 rounded-full border border-muted-foreground/60 dark:border-muted-foreground/90"
                disabled={!value.trim()}
                onClick={submit}
                size="icon"
                type="button"
                variant="default"
              >
                <ArrowUpIcon className="size-5" />
              </Button>
            )}
          </div>
        </ComposerPrimitive.Root>
      </div>
    </div>
  );
}
