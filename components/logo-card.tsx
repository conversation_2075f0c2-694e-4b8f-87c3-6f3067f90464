import { Download } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { SelectLogo } from '@/db/schema';

interface LogoCardProps {
  logo: SelectLogo;
  onDownload: () => void;
  /**
   * When provided, shows a "Set as cover" action on the card.
   * Called with the logo id to set as the project cover.
   */
  onSetCover?: (logoId: number) => void;
  /**
   * Whether this logo is currently the project's cover image.
   */
  isCover?: boolean;
  /**
   * Whether to show style/prompt metadata below the image.
   * Defaults to true for contexts like search or history; can be hidden on detail pages
   * where the prompt is already summarized in the header.
   */
  showMeta?: boolean;
  /**
   * Whether to show color chips below the image. Defaults to true.
   */
  showColors?: boolean;
  /** Hide the "Open in Generator" action (project page shows project-level button) */
  showOpenInGenerator?: boolean;
}

/**
 * LogoCard
 *
 * Purpose: Present a generated logo in an image-first card with clear, always-visible actions.
 * Actions are not hidden behind hover to keep affordances obvious.
 *
 * @param logo - The logo record to render (image, prompt, style, colors, metadata)
 * @param onDownload - Handler that downloads the logo image
 * @param onSetCover - Optional handler to set this logo as project cover
 * @param isCover - Whether this logo is already the cover
 * @param showMeta - Show style/prompt metadata (default true)
 * @param showColors - Show color chips (default true)
 * @returns JSX.Element
 * @example
 * <LogoCard logo={l} onDownload={(u)=>{}} onSetCover={(id)=>{}} isCover={false} />
 */
const LogoCard = ({
  logo,
  onDownload,
  onSetCover,
  isCover = false,
  showMeta = true,
  showColors = true,
  showOpenInGenerator = true,
}: LogoCardProps) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const generatorHref = useMemo(() => {
    const qp = new URLSearchParams();
    qp.set('project', logo.projectName);
    if (logo.prompt) {
      qp.set('prompt', logo.prompt);
    }
    if (logo.style) {
      qp.set('style', logo.style);
    }
    if (logo.primary_color) {
      qp.set('primaryColor', logo.primary_color);
    }
    if (logo.background_color) {
      qp.set('backgroundColor', logo.background_color);
    }
    if (logo.image_url) {
      qp.set('imageUrl', logo.image_url);
    }
    // Include logoId to allow the editor to load the full original pair (siblings)
    if (logo.id) {
      qp.set('logoId', String(logo.id));
    }
    return `/generate?${qp.toString()}`;
  }, [
    logo.background_color,
    logo.id,
    logo.primary_color,
    logo.projectName,
    logo.prompt,
    logo.style,
    logo.image_url,
  ]);

  return (
    <Card className="group rounded-2xl">
      <CardContent className="w-full rounded-2xl">
        <div className="relative aspect-square w-full overflow-hidden rounded-t-2xl">
          {!imageLoaded && (
            <div className="absolute inset-0 animate-pulse bg-slate-200" />
          )}
          <Image
            alt={`${logo.username}'s logo`}
            className={`object-contain transition-all duration-700 ease-in-out group-hover:scale-105 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            fill
            onLoad={() => setImageLoaded(true)}
            src={logo.image_url}
          />
          {isCover && (
            <div className="absolute top-3 left-3">
              <Badge className="shadow-sm" variant="secondary">
                Cover
              </Badge>
            </div>
          )}
        </div>
        <div
          className={`border-t p-4 transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-40'}`}
        >
          {/* Meta row */}
          {showMeta && (
            <div className="flex items-start gap-2">
              {logo.style && <Badge variant="outline">{logo.style}</Badge>}
              {logo.prompt && (
                <p
                  className="line-clamp-2 text-muted-foreground text-sm"
                  title={logo.prompt}
                >
                  <span className="font-medium">Prompt: </span>
                  {logo.prompt}
                </p>
              )}
            </div>
          )}
          {/* Colors */}
          {showColors && (
            <div className="mt-3 flex items-center gap-2">
              <div
                className="h-5 w-5 rounded-[8px] border"
                style={{ backgroundColor: logo.primary_color }}
                title="Primary Color"
              />
              <div
                className="h-5 w-5 rounded-[8px] border"
                style={{ backgroundColor: logo.background_color }}
                title="Background Color"
              />
            </div>
          )}
          {/* Actions - always visible */}
          <div className="mt-4 flex flex-wrap gap-2">
            {showOpenInGenerator && (
              <Button asChild>
                <Link href={generatorHref} prefetch={false}>
                  Open in Generator
                </Link>
              </Button>
            )}
            <Button onClick={onDownload} variant="secondary">
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            {onSetCover && !isCover && (
              <Button onClick={() => onSetCover(logo.id)} variant="outline">
                Set as cover
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LogoCard;
