{"name": "superlogo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "npx ultracite@latest lint", "format": "npx ultracite@latest format", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/openai": "^2.0.13", "@assistant-ui/react": "^0.10.41", "@assistant-ui/react-ai-sdk": "^1.0.1", "@assistant-ui/react-markdown": "^0.10.8", "@clerk/nextjs": "^6.30.1", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.8", "@tabler/icons-react": "^3.34.1", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "ai": "^5.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dedent": "^1.6.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next": "15.4.6", "next-themes": "^0.4.6", "openai": "^5.12.2", "react": "^19.1.1", "react-dom": "^19.1.1", "remark-gfm": "^4.0.1", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@biomejs/biome": "^2.1.4", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.17.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "drizzle-kit": "^0.31.4", "husky": "^9.1.7", "postcss": "^8.5.6", "tailwindcss": "4.1.11", "typescript": "^5.9.2", "ultracite": "^5.1.2"}}