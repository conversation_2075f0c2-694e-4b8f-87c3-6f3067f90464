import OpenAI from 'openai';

export function getOpenAIClient() {
  const apiKey = process.env.NEBIUS_API_KEY;
  if (!apiKey) {
    throw new Error('NEBIUS_API_KEY is not defined');
  }

  const { HELICONE_API_KEY } = process.env;

  const clientOptions: ConstructorParameters<typeof OpenAI>[0] = {
    apiKey,
    baseURL: HELICONE_API_KEY
      ? 'https://nebius.helicone.ai/v1/'
      : 'https://api.studio.nebius.ai/v1/',
    ...(HELICONE_API_KEY && {
      defaultHeaders: { 'Helicone-Auth': `Bearer ${HELICONE_API_KEY}` },
    }),
  };

  return new OpenAI(clientOptions);
}
