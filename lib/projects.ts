import type { Project, ViewMode } from '@/types/project';

/**
 * Filters projects by query across name and prompt.
 *
 * @param projects - List of projects to filter
 * @param query - Case-insensitive search string
 * @returns Filtered list of projects
 * @example
 * filterProjects([{ projectName: 'Alpha' } as Project], 'alp') // => [{...}]
 */
export function filterProjects(projects: Project[], query: string): Project[] {
  const q = query.trim().toLowerCase();
  if (q.length === 0) {
    return projects;
  }
  return projects.filter(
    (p) =>
      p.projectName.toLowerCase().includes(q) ||
      (p.prompt?.toLowerCase().includes(q) ?? false)
  );
}

/**
 * Sorts projects by the selected mode.
 *
 * @param projects - List of projects to sort
 * @param sortBy - Sorting mode
 * @returns New array of projects sorted by the mode
 * @example
 * sortProjects(projects, 'a-z') // => alphabetical
 */
export function sortProjects(
  projects: Project[],
  sortBy: 'recent' | 'a-z' | 'favorites'
): Project[] {
  const copy = [...projects];
  if (sortBy === 'a-z') {
    return copy.sort((a, b) => a.projectName.localeCompare(b.projectName));
  }
  if (sortBy === 'favorites') {
    return copy.sort((a, b) => {
      if (a.isFavorite !== b.isFavorite) {
        return a.isFavorite ? -1 : 1;
      }
      return (
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
      );
    });
  }
  // recent
  return copy.sort(
    (a, b) =>
      new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
  );
}

/**
 * Formats an ISO date string for display.
 *
 * @param dateString - ISO string
 * @returns Locale-formatted date (MMM D, YYYY)
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
}

/**
 * Reads the saved projects view mode from localStorage.
 * Defaults to 'grid' if not set or on any error.
 *
 * NOTE: Client-only. Guards for SSR.
 *
 * @returns {'grid' | 'list'} Previously saved mode or 'grid'.
 */
export function getSavedViewMode(): ViewMode {
  try {
    if (typeof window !== 'undefined') {
      const v = localStorage.getItem('projects:viewMode');
      if (v === 'grid' || v === 'list') {
        return v;
      }
    }
  } catch {
    // ignore storage errors
  }
  return 'grid';
}

/**
 * Persists the projects view mode to localStorage.
 *
 * NOTE: Client-only. Guards for SSR.
 *
 * @param mode - The view mode to persist.
 */
export function saveViewMode(mode: ViewMode): void {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem('projects:viewMode', mode);
    }
  } catch {
    // ignore storage errors
  }
}
