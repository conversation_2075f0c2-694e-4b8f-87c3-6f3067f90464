import { eq, sql } from 'drizzle-orm';
import { db } from '@/db';
import { creditTransactionsTable, userCreditsTable } from '@/db/schema';
import type { Plan } from './plan';

export function getMonthlyCreditsForPlan(plan: Plan): number {
  switch (plan) {
    case 'free':
      return 3;
    case 'starter':
      return 50;
    case 'pro':
      return 200;
    case 'elite':
      return 600;
    default:
      return 3;
  }
}

export async function ensureUserCredits(userId: string, plan: Plan) {
  const monthly = getMonthlyCreditsForPlan(plan);
  const now = new Date();
  await db
    .insert(userCreditsTable)
    .values({ userId, plan, creditsRemaining: monthly, refilledAt: now })
    .onConflictDoNothing();
}

export async function refillCredits(userId: string, plan: Plan) {
  const monthly = getMonthlyCreditsForPlan(plan);
  const now = new Date();
  await db
    .insert(userCreditsTable)
    .values({ userId, plan, creditsRemaining: monthly, refilledAt: now })
    .onConflictDoUpdate({
      target: userCreditsTable.userId,
      set: { plan, creditsRemaining: monthly, refilledAt: now },
    });
  await db.insert(creditTransactionsTable).values({
    userId,
    delta: monthly,
    reason: 'refill',
  });
}

export async function decrementCredit(userId: string) {
  const result = await db.execute(
    sql`UPDATE user_credits SET credits_remaining = credits_remaining - 1 WHERE user_id = ${userId} AND credits_remaining > 0 RETURNING credits_remaining;`
  );
  const row = (result as unknown as { rows: { credits_remaining: number }[] })
    .rows?.[0];
  if (!row) {
    return { success: false as const };
  }
  await db.insert(creditTransactionsTable).values({
    userId,
    delta: -1,
    reason: 'generation',
  });
  return { success: true as const, remaining: row.credits_remaining };
}

export async function getRemainingCredits(userId: string) {
  const rows = await db
    .select()
    .from(userCreditsTable)
    .where(eq(userCreditsTable.userId, userId));
  return rows[0]?.creditsRemaining ?? 0;
}
