export type Plan = 'free' | 'starter' | 'pro' | 'elite';

export function getMonthlyLimitForPlan(plan: Plan): number {
  switch (plan) {
    case 'free':
      return 3;
    case 'starter':
      return 50;
    case 'pro':
      return 200;
    case 'elite':
      return 600;
    default:
      return 3;
  }
}

export function getUserPlanFromEntitlements(entitlements?: string[]): Plan {
  if (!entitlements || entitlements.length === 0) {
    return 'free';
  }
  // New keys preferred
  if (entitlements.includes('key_elite')) {
    return 'elite';
  }
  if (entitlements.includes('key_pro')) {
    return 'pro';
  }
  if (entitlements.includes('key_starter')) {
    return 'starter';
  }
  // Backward-compat keys (without key_ prefix)
  if (entitlements.includes('elite')) {
    return 'elite';
  }
  if (entitlements.includes('pro')) {
    return 'pro';
  }
  if (entitlements.includes('starter')) {
    return 'starter';
  }
  return 'free';
}
